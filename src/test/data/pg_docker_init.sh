#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-<PERSON>OSQL
	create schema if not exists $POSTGRES_SCHEMA;

  CREATE TABLE IF NOT EXISTS core_db.customers
  (
      id                       bigint                   NOT NULL,
      password                 varchar(255)                NULL,
      active                   boolean                    DEFAULT TRUE,
      status                   varchar(255) NOT NULL DEFAULT 'ACTIVE',

      created_by               varchar(255)                NULL,
      updated_by               varchar(255)                NULL,

      created_on             timestamptz          NOT NULL,
      updated_on             timestamptz          NOT    NULL,
      deleted_at               bigint              NULL,

      phone                    varchar(20)                 NULL,
      phone_verified           smallint                    NOT NULL DEFAULT 0,

      email                    varchar(200)                NULL,
      email_verified           smallint                    NOT NULL DEFAULT 0,

      oauth_google_id          varchar(32)                          DEFAULT NULL,
      oauth_facebook_id        varchar(32)                          DEFAULT NULL,
      oauth_apple_id           varchar(64)                          DEFAULT NULL,
      oauth_azure_id           varchar(64)                          DEFAULT NULL,
      auth_firebase_id         varchar(64)                          DEFAULT NULL,
      devices                  jsonb                       NULL,

      first_name               varchar(255)                NULL,
      last_name                varchar(255)                NULL,
      full_name                varchar(255)                NULL,
      gender                   varchar(255)                NULL,
      avatar                   varchar(255)                NULL,
      birthday                 date                        NULL,
      nationality_code         varchar(255)                NULL,
      list_address             jsonb                       NULL,
      list_identity_document   jsonb                       NULL,

      metadata                 jsonb                       NULL,
      pnl_mapping              jsonb                       NULL,
      pnl_ranking_progress              jsonb                       NULL,

      ---
      --- Old fields
      ---

      version                  smallint                    NULL,
      aquisition_channel       varchar(255)                NULL,
  --     avatar_name            jsonb                       NULL,
  --     company_address        varchar(255)                NULL,
  --     company_name           varchar(255)                NULL,
  --     company_phone          varchar(255)                NULL,
  --     country_code           varchar(255)                NULL,
  --     identities             jsonb                       NULL,
  --     job_position           varchar(255)                NULL,
  --     job_title              varchar(255)                NULL,
      org_id                   int8                        NULL,
  --     original_id              varchar(255)                NULL,
  --     phone_code               varchar(255)                NULL,
  --     reference_data           jsonb                       NULL,
      segments                 INTEGER[]                   NULL,
      social_id1               varchar(255)                NULL,
      social_id2               varchar(255)                NULL,
      social_id3               varchar(255)                NULL,
      "source"                 varchar(255)                NULL,
  --     status                   varchar(255)                NOT NULL,
      tags                     INTEGER[]                   NULL,
      tenant_id                int8                        NULL,
      tier_ids                 INTEGER[]                   NULL,
      username                 varchar(255)                NULL,
      zipcode                  varchar(255)                NULL,
      acquisition_channel      varchar(255)                NULL,
      is_online_customer       bool                        NULL,
      "domain"                 varchar(255)                NULL,
      source_system_id         varchar(255)                NULL,
      one_id                   varchar(255)                NULL,
      profile_id               varchar(255)                NULL,
      last_order_date          timestamp                   NULL,
      other_ids                TEXT[]                      NULL,
      sap_id                   varchar(255)                NULL,
      tel_escooter             varchar(255)                NULL,
      preferred_language       varchar(255)                NULL,
      imported_from            varchar(255)                NULL,
      cdp_id                   varchar(255)                NULL,
      count_in_last_order_date int4                        NULL,
      referred_by              varchar(255)                NULL,
      referred_by_id           int8                        NULL,
      source_created_on        timestamp                   NULL,
      source_updated_on        timestamp                   NULL,
      original_phone_number    varchar(255)                NULL,
      demographic_metadata             jsonb                       NULL,
      demographic_metadata_extra             jsonb                       NULL,
      normalized_email              varchar(255)                NULL,
      display_setting    jsonb NULL,
      referral_code    varchar(100) NULL,
      CONSTRAINT customers_pkey PRIMARY KEY (id)
  );
  --CREATE INDEX IF NOT EXISTS customer_created_on_date_idx ON core_db.customers USING btree (date(created_on));
  -- CREATE INDEX IF NOT EXISTS customer_idx_1 ON core_db.customers USING btree (original_id);
  CREATE INDEX IF NOT EXISTS customer_idx_10 ON core_db.customers USING btree (phone);
  CREATE INDEX IF NOT EXISTS customer_idx_11 ON core_db.customers USING btree (active);
  -- CREATE INDEX IF NOT EXISTS customer_idx_12 ON core_db.customers USING btree (full_name, email, phone_number, original_id);
  CREATE INDEX IF NOT EXISTS customer_idx_13 ON core_db.customers USING btree (profile_id);
  CREATE INDEX IF NOT EXISTS customer_idx_14 ON core_db.customers USING btree (sap_id);
  CREATE INDEX IF NOT EXISTS customer_idx_15 ON core_db.customers USING btree (domain);
  CREATE INDEX IF NOT EXISTS customer_idx_16 ON core_db.customers USING btree (imported_from);
  CREATE INDEX IF NOT EXISTS customer_idx_17 ON core_db.customers USING btree (updated_on);
  CREATE INDEX IF NOT EXISTS customer_idx_18 ON core_db.customers USING btree (tags, last_order_date);
  CREATE INDEX IF NOT EXISTS customer_idx_19 ON core_db.customers USING btree (tags, count_in_last_order_date);
  CREATE INDEX IF NOT EXISTS customer_idx_2 ON core_db.customers USING btree (email);
  CREATE INDEX IF NOT EXISTS customer_idx_20 ON core_db.customers USING btree (segments, id);
  CREATE INDEX IF NOT EXISTS customer_idx_21 ON core_db.customers USING btree (referred_by);
  CREATE INDEX IF NOT EXISTS customer_idx_22 ON core_db.customers USING btree (created_on);
  CREATE INDEX IF NOT EXISTS customer_idx_23 ON core_db.customers USING btree (source_created_on);
  CREATE INDEX IF NOT EXISTS customer_idx_24 ON core_db.customers USING btree (original_phone_number);
  -- CREATE INDEX IF NOT EXISTS customer_idx_25 ON core_db.customers USING btree (((metadata ->> 'vfcwReferralCode':: text)));
  -- CREATE INDEX IF NOT EXISTS customer_idx_26 ON core_db.customers USING btree (((metadata ->> 'gsmDriverReferralCode':: text)));
  CREATE INDEX IF NOT EXISTS customer_idx_3 ON core_db.customers USING btree (tags, active);
  CREATE INDEX IF NOT EXISTS customer_idx_4 ON core_db.customers USING btree (segments);
  CREATE INDEX IF NOT EXISTS customer_idx_6 ON core_db.customers USING btree (email, segments);
  CREATE INDEX IF NOT EXISTS customer_idx_8 ON core_db.customers USING btree (source_system_id);
  CREATE INDEX IF NOT EXISTS customer_idx_9 ON core_db.customers USING btree (full_name);
  CREATE INDEX IF NOT EXISTS customer_segments_gin_idx ON core_db.customers USING gin (segments);
  CREATE INDEX IF NOT EXISTS customer_tags_gin_idx ON core_db.customers USING gin (tags);

  CREATE TABLE IF NOT EXISTS core_db.session
  (
    session_id varchar(64) NOT NULL,
    user_id bigint NULL,
    created_time bigint NOT NULL,
    last_access_time bigint NOT NULL,
    timeout bigint NOT NULL,
    serialize text NULL,
    CONSTRAINT session_pkey PRIMARY KEY (session_id)
  );
  CREATE INDEX IF NOT EXISTS session_idx_1 ON core_db.session USING btree (user_id);

EOSQL
