package profile.service


import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.twitter.inject.Logging
import com.twitter.inject.conversions.pattern.RichRegex
import com.twitter.util.{Await, Future}
import com.typesafe.config.Config
import org.apache.commons.io.FileUtils
import profile.domain.BytesFile
import profile.domain.customer.EKYCDocumentType.{CHIP_BASED_ID_CARD, EKYCDocumentType, ID_CARD, PASSPORT, WHITE_ID_CARD}
import profile.domain.customer.EKYCVerifyStep.{SCAN_BACK_DOCUMENT, SCAN_FACE, SCAN_FRONT_DOCUMENT, SCAN_NFC}
import profile.domain.customer.{CustomerIdentityChipData, CustomerIdentityDocType, CustomerIdentityDocument, CustomerIdentityVerificationScores, EKYCDocumentType, EKYCDocumentVerification, EKYCVerifyStep}
import profile.util.{Constant, CustomUtils}
import scalaj.http.Http
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{InternalError, NotFoundException, RCustomException, VhmCustomException}
import vn.vhm.common.repository.cache.RedisCacheCommon
import vn.vhm.common.service.DebugServiceNotification
import vn.vhm.common.util.JsonHelper

import java.io.File
import java.util.{Base64, UUID}
import javax.crypto.Cipher
import javax.crypto.spec.{GCMParameterSpec, SecretKeySpec}

/**
 * <AUTHOR> 01/24/25 11:14
 */
trait VbdEyePassIntegrationService {

  def requestAccessToken(customerId: String, loadCacheOnly: Boolean = false): Future[Option[VbdSdkAuthInfo]]

  def verifyDocument(customerId: String, vbdAccessTokenOpt: Option[String], documentInfoOpt: Option[VBDDocumentInfo], requestIds: Option[Map[String, String]], lang: Option[String] = None): Future[EKYCDocumentVerification]

  def fetchDocument(documentId: String, lang: String = Constant.DEFAULT_LANGUAGE): Future[Option[VBDDocumentInfo]]

  def feedback(documentId: String, requestId: String, feedback: Feedback, note: Option[String]): Future[FeedbackResponse]
}

case class VbdEyePassIntegrationServiceImpl(vbdConfig: Config, keyValueRepo: RedisCacheCommon) extends VbdEyePassIntegrationService with Logging {
  // config
  private val baseUrl = vbdConfig.getString("base_url")
  private val appId = vbdConfig.getString("app_id")
  private val appSecret = vbdConfig.getString("app_secret")
  private val encryptKey = vbdConfig.getString("encrypt_key")
  private val delayTokenExpireTimeInSecond = 10

  // document config
  private val documentTypeSupported = vbdConfig.getString("document_type_supported").split(",").map(_.trim).filter(_.nonEmpty).toSeq
  private val idCardVerifySteps = vbdConfig.getString("id_card_verify_steps").split(",").map(_.trim).filter(_.nonEmpty).toSeq
  private val chipBasedIdCardVerifySteps = vbdConfig.getString("chip_based_id_card_verify_steps").split(",").map(_.trim).filter(_.nonEmpty).toSeq
  private val whiteIdCardVerifySteps = vbdConfig.getString("white_id_card_verify_steps").split(",").map(_.trim).filter(_.nonEmpty).toSeq
  private val legacyIdVerifySteps = vbdConfig.getString("legacy_id_verify_steps").split(",").map(_.trim).filter(_.nonEmpty).toSeq
  private val passportVerifySteps = vbdConfig.getString("passport_verify_steps").split(",").map(_.trim).filter(_.nonEmpty).toSeq


  // face flow config
  private val useFaceCompare = vbdConfig.getBoolean("use_face_compare")
  private val useFaceLiveness = vbdConfig.getBoolean("use_face_liveness")

  // API URLs
  private val loginUrl = s"$baseUrl/api/auth/sdk/login"
  private val getDocumentUrl = s"$baseUrl/api/v1/documents/:document_id"
  private val feedbackUrl = s"$baseUrl/api/v1/third-party/vinclub/feedback"

  private val validationSummaryPassed = 1
  private val providerName = "VBD_SDK"
  private val cacheIntegrationPrefix = Constant.CACHE_INTEGRATION

  // enable test scenarios
  private val enableTestScenarios = vbdConfig.getString("enable_test_scenarios")

  override def requestAccessToken(customerId: String, loadCache: Boolean = false): Future[Option[VbdSdkAuthInfo]] = {
    val key = s"vbd_sdk_token:$customerId"
    if (loadCache) fetchFromCache(key) else _loginToVbdSdk(key)
  }

  private def fetchFromCache(key: String): Future[Option[VbdSdkAuthInfo]] = {
    keyValueRepo.cacheGet(cacheIntegrationPrefix, key).flatMap({
      case Some(token) =>
        try {
          async {
            info(s"Get VBD SDK token from cache: $key")
            JsonHelper.fromJson[VbdSdkAuthInfo](token).toSome
          }
        } catch {
          case e: JsonProcessingException => async {
            error(s"Failed to parse cached token: ${e.getMessage}")
            None
          }
          case e: Exception => async {
            error(s"Unknown exception when parsing cached token: ${e.getMessage}")
            None
          }
        }
      case None =>
        Future.None
    })
  }

  private def _loginToVbdSdk(key: String): Future[Option[VbdSdkAuthInfo]] = Profiler(s"${getClass.getCanonicalName}._loginToVbdSdk") {
    val resp = Http(loginUrl)
      .timeout(30000, 30000)
      .postForm(Seq("app_id" -> appId, "app_secret" -> appSecret))
      .asString

    info(s"_loginToVbdSdk($key)\t${resp.code}")

    resp.code match {
      case 200 =>
        try {
          val jsonNode = JsonHelper.readTree(resp.body)

          jsonNode.at("/code").asInt(0) match {
            case 0 =>
              val accessToken = jsonNode.at("/data/access").asText("")
              val refreshToken = jsonNode.at("/data/refresh").asText("")
              val username = jsonNode.at("/data/username").asText("")
              val expireIn = jsonNode.at("/data/expire_in").asLong(0)
              val serverTime = jsonNode.at("/server_time").asLong(0)
              val requestId = jsonNode.at("/request_id").asText("")
              val vbdSdkAuthInfo = VbdSdkAuthInfo(accessToken = Some(accessToken), refreshToken = Some(refreshToken), username = Some(username), expireIn = Some(expireIn), serverTime = Some(serverTime), requestId = Some(requestId))
              vbdSdkAuthInfo.additionalConfigs = getAdditionalConfig.toSome
              keyValueRepo.cacheSet(cacheIntegrationPrefix, key, expireIn.toInt - delayTokenExpireTimeInSecond, JsonHelper.toJson(vbdSdkAuthInfo)).map(_ => vbdSdkAuthInfo.toSome)
            case _ => async {
              error(s"Failed to login to VBD SDK: ${resp.code} - ${resp.body}")
              None
            }
          }
        } catch {
          case e: JsonProcessingException => async {
            error(s"Failed to login to VBD SDK: ${resp.code} - ${resp.body}: ${e.getMessage}")
            None
          }
          case e: java.net.SocketTimeoutException => async {
            error(s"Failed to login to VBD SDK: ${resp.code} - ${resp.body}: ${e.getMessage}")
            DebugServiceNotification.notify(s"Failed to login to VBD SDK: ${resp.code} - ${resp.body}: ${e.getClass.getCanonicalName} - ${e.getMessage}")
            throw InternalError(s"Timeout exception: ${e.getMessage}")
          }
          case e: Exception => async {
            error(s"Failed to login to VBD SDK: ${resp.code} - ${resp.body}: ${e.getMessage}")
            throw InternalError(s"Unknown exception: ${e.getMessage}")
          }
        }
      case _ => async {
        error(s"Failed to login to VBD SDK: ${resp.code} - ${resp.body}")
        None
      }
    }
  }

  private def getAdditionalConfig: Map[String, Any] = {
    def checkIsEnableNfc: Boolean = {
      documentTypeSupported.contains(EKYCDocumentType.ID_CARD.toString) && idCardVerifySteps.contains(EKYCVerifyStep.SCAN_NFC.toString) || (documentTypeSupported.contains(EKYCDocumentType.CHIP_BASED_ID_CARD.toString) && chipBasedIdCardVerifySteps.contains(EKYCVerifyStep.SCAN_NFC.toString))
    }

    Map[String, Any](
      "use_nfc" -> checkIsEnableNfc,
      "use_face_compare" -> useFaceCompare,
      "use_face_liveness" -> useFaceLiveness,
      "document_type_supported" -> documentTypeSupported,
      "encrypt_key" -> encryptKey
    )
  }


  override def verifyDocument(customerId: String, vbdAccessTokenOpt: Option[String], documentInfoOpt: Option[VBDDocumentInfo], requestIds: Option[Map[String, String]], lang: Option[String] = None): Future[EKYCDocumentVerification] = Profiler(s"${getClass.getCanonicalName}.verifyDocument") {

    val vbdAccessToken = vbdAccessTokenOpt.getOrElse(throw RCustomException("invalid_param", "vbd_access_token"))
    val requestDocument = documentInfoOpt.getOrElse(throw RCustomException("invalid_param", "document_info"))
    val documentId = requestDocument.documentId
    val langToGet = lang.getOrElse(Constant.DEFAULT_LANGUAGE)

    requestAccessToken(customerId, loadCache = true).flatMap({
      case Some(vbdSdkAuthInfo) =>
        validateAccessToken(vbdAccessToken, vbdSdkAuthInfo)
        validateRequestDocument(requestDocument)

        var identityImages: Option[Seq[BytesFile]] = None
        var identityRawImages: Option[Seq[BytesFile]] = None
        var faceImages: Option[Seq[BytesFile]] = None
        var chipImages: Option[Seq[BytesFile]] = None

        fetchDocument(documentId, vbdAccessToken, langToGet).map { vbdDocument =>
          validateAndCompareVBDDocument(vbdDocument, requestDocument)

          // download images from VBD server
          identityImages = downloadIdentityImages(vbdDocument)
          identityRawImages = downloadRawIdentityImages(vbdDocument)
          faceImages = downloadFaceImages(vbdDocument)
          chipImages = downloadChipImages(vbdDocument)

          // extract data from VBD document (must be in default language)
          val documentToExtract = langToGet match {
            case Constant.DEFAULT_LANGUAGE => vbdDocument
            case _ => Await.result(fetchDocument(documentId, vbdAccessToken, Constant.DEFAULT_LANGUAGE))
          }

          buildEKYCDocumentVerification(JsonHelper.fromNode[VBDDocumentInfo](documentToExtract), identityImages, faceImages, identityRawImages, chipImages, requestIds)
        } rescue {
          case e: RCustomException =>
            cleanupFiles(identityImages, faceImages, chipImages, identityRawImages)
            throw e
          case e: Exception =>
            cleanupFiles(identityImages, faceImages, chipImages, identityRawImages)
            error(s"Failed to get document: ${e.getMessage}")
            throw RCustomException("failed_to_get_document", s"Failed to get document: ${e.getMessage}")
        }
      case _ => Future.exception(RCustomException("expired_vbd_access_token", "VBD access token is expired"))
    })
  }

  override def fetchDocument(documentId: String, lang: String = Constant.DEFAULT_LANGUAGE): Future[Option[VBDDocumentInfo]] = Profiler(s"${getClass.getCanonicalName}.fetchDocument") {
    // Get access token using a system customer ID for internal operations
    val systemCustomerId = "system_migration"
    requestAccessToken(systemCustomerId, loadCache = true).or(requestAccessToken(systemCustomerId)).flatMap({
      case Some(vbdSdkAuthInfo) =>
        val vbdAccessToken = vbdSdkAuthInfo.accessToken.getOrElse(throw RCustomException("expired_vbd_access_token", "VBD access token is expired"))
        fetchDocument(documentId, vbdAccessToken, lang).map { vbdDocument =>
          Some(JsonHelper.fromNode[VBDDocumentInfo](vbdDocument))
        }
      case _ => Future.exception(RCustomException("expired_vbd_access_token", "VBD access token is expired"))
    })
  }

  private def validateAccessToken(vbdAccessToken: String, vbdSdkAuthInfo: VbdSdkAuthInfo): Unit = {
    if (vbdSdkAuthInfo.accessToken.exists(_ != vbdAccessToken)) {
      throw RCustomException("invalid_vbd_access_token", "VBD access token is invalid")
    }
  }

  private def validateRequestDocument(requestDocument: VBDDocumentInfo): Unit = {
    if (!checkDocumentValidationInfo(JsonHelper.readTree(JsonHelper.toJson(requestDocument.validationInfo)))) {
      error(s"Invalid request document verification data: ${requestDocument.documentId}")
      throw RCustomException("document_verification_failed", "Request document verification data is invalid")
    }
  }

  private def validateAndCompareVBDDocument(vbdDocument: JsonNode, requestDocument: VBDDocumentInfo): Unit = {
    if (!checkDocumentValidationInfo(vbdDocument.at("/validation_info"))) {
      error(s"Invalid VBD document verification data:${vbdDocument.at("document_id").asText()}")
      throw RCustomException("document_verification_failed", "VBD document verification data is invalid")
    }

    if (!compareDocumentData(vbdDocument, requestDocument)) {
      error(s"Document verification failed: ${requestDocument.documentId} - Document is not match with VBD data")
      throw RCustomException("document_verification_failed", "Request document is not match with VBD document")
    }
  }

  private def fetchDocument(documentId: String, token: String, language: String): Future[JsonNode] = async {
    var documentUrl = getDocumentUrl.replace(":document_id", documentId)
    if (enableTestScenarios == "verify_document_call_api_fail") {
      warn(s"Test scenario: verify_document_call_api_fail")
      documentUrl = documentUrl + "1" // append "1" to make the URL invalid
    }

    val response = Http(documentUrl)
      .timeout(30000, 30000)
      .header("Authorization", s"Bearer $token")
      .header("Accept-Language", language)
      .asString

    info(s"fetchVBDDocument($documentId, $language) => ${response.code}")

    if (response.code == 200) {
      val responseNode = JsonHelper.readTree(response.body)
      val decryptedDocument = decryptIfNeeded(responseNode)
      if (isEmptyNode(decryptedDocument) || isEmptyNode(decryptedDocument.at("/validation_info"))) {
        error(s"Invalid document from VBD API: ${documentId} - Data is missing")
        throw RCustomException("failed_to_get_document", "Document data from VBD is invalid")
      }
      decryptedDocument
    }
    else throw RCustomException("failed_to_get_document", s"Failed to get document: ${response.code}")
  }

  private def decryptIfNeeded(jsonNode: JsonNode): JsonNode = {
    val encryptDataStr = jsonNode.at("/encrypted_data").asText("")
    if (encryptDataStr.isEmpty) jsonNode.at("/data") else decryptResponseData(encryptDataStr, encryptKey)
  }

  private def cleanupFiles(identityImages: Option[Seq[BytesFile]], faceImages: Option[Seq[BytesFile]], chipImages: Option[Seq[BytesFile]], identityRawImages: Option[Seq[BytesFile]]): Unit = {
    identityImages.foreach(_.foreach(file => FileUtils.deleteQuietly(new File(file.localPath))))
    faceImages.foreach(_.foreach(file => FileUtils.deleteQuietly(new File(file.localPath))))
    chipImages.foreach(_.foreach(file => FileUtils.deleteQuietly(new File(file.localPath))))
    identityRawImages.foreach(_.foreach(file => FileUtils.deleteQuietly(new File(file.localPath))))
  }

  private def buildEKYCDocumentVerification(documentInfo: VBDDocumentInfo, identityImages: Option[Seq[BytesFile]], faceImages: Option[Seq[BytesFile]], identityRawImages: Option[Seq[BytesFile]], chipImages: Option[Seq[BytesFile]], requestIds: Option[Map[String, String]]): EKYCDocumentVerification = {
    val documentType = EKYCDocumentType.get(documentInfo.validationInfo.ocr.get.`type`)

    val identityType = documentType match {
      case ID_CARD | CHIP_BASED_ID_CARD | WHITE_ID_CARD => CustomerIdentityDocType.CCCD
      case PASSPORT => CustomerIdentityDocType.PASSPORT
      case _ => throw VhmCustomException("invalid_param", "identity_type")
    }

    val identityNo = documentInfo.validationInfo.ocr.flatMap(_.idNumber).map(_.value)

    val identityDoc = CustomerIdentityDocument(
      `type` = identityType.toString.toSome,
      no = identityNo,
      verifiedStatus = None, verifiedStatusMessage = None,
      identityDocumentVerifyId = None
    )
    val identityInfo: CustomerIdentityDocumentInfo = CustomerIdentityDocumentInfo(
      identityType = identityType.toString,
      no = identityNo.get,
      fullName = documentInfo.validationInfo.ocr.flatMap(_.name).map(_.value),
      dob = documentInfo.validationInfo.ocr.flatMap(_.dob).map(_.value),
      gender = documentInfo.validationInfo.ocr.flatMap(_.gender).map(_.value),
      issuedBy = documentInfo.validationInfo.ocr.flatMap(_.poi).map(_.value),
      issuedDate = documentInfo.validationInfo.ocr.flatMap(_.doi).map(_.value),
      expiredDate = documentInfo.validationInfo.ocr.flatMap(_.doe).map(_.value),
      hometown = documentInfo.validationInfo.ocr.flatMap(ocr => ocr.hometown.orElse(ocr.pob)).map(_.value),
      address = documentInfo.validationInfo.ocr.flatMap(_.address).map(_.value),
      ocrAddress = documentInfo.validationInfo.ocr.flatMap(_.address).map(_.value),
      city = documentInfo.validationInfo.ocr.flatMap(_.city).map(_.value),
      nationality = documentInfo.validationInfo.ocr.flatMap(_.nationality).map(_.value),
      mrzId = documentInfo.validationInfo.ocr.flatMap(_.mrzInfo).flatMap(_.code).map(_.value),
      documentType = documentType.toString.toSome
    )

    val faceMatchingScore = CustomerIdentityVerificationScores(
      faceMatching = documentInfo.validationInfo.face.flatMap(_.compareData).flatMap(_.personCardValidation).flatMap(_.similarityPercentage).map(_ / 100),
      faceMatchingLabel = documentInfo.validationInfo.face.flatMap(_.compareData).flatMap(_.personCardValidation).flatMap(_.isSamePerson) match {
        case Some(true) => "matched".toSome
        case Some(false) => "unmatched".toSome
        case _ => None
      },
      faceChipMatching = documentInfo.validationInfo.face.flatMap(_.compareData).flatMap(_.personChipValidation).flatMap(_.similarityPercentage).map(_ / 100),
      faceChipMatchingLabel = documentInfo.validationInfo.face.flatMap(_.compareData).flatMap(_.personChipValidation).flatMap(_.isSamePerson) match {
        case Some(true) => "matched".toSome
        case Some(false) => "unmatched".toSome
        case _ => None
      }
    )

    val nextStep = _getVerifySteps(documentType.toSome).map(EKYCVerifyStep.opt).find {
      case Some(SCAN_FRONT_DOCUMENT) => identityImages.forall(_.size < 1)
      case Some(SCAN_BACK_DOCUMENT) => identityImages.forall(_.size < 2)
      case Some(SCAN_NFC) => documentInfo.validationInfo.chip.isEmpty
      case Some(SCAN_FACE) => faceImages.isEmpty
      case _ => false
    }.flatten

    val metadata = Map[String, Any](
      "document_id" -> documentInfo.documentId,
      "face_id" -> documentInfo.faceId.orNull,
      "quality_warnings" -> documentInfo.qualityWarnings.orNull,
      "vbd_request_ids" -> requestIds.orNull
    ).filter(_._2 != null).toSome

    val chipData: Option[CustomerIdentityChipData] = documentInfo.validationInfo.chip match {
      case Some(chip) =>
        val data = CustomerIdentityChipData(
          documentNo = chip.idNumber.map(_.value),
          documentNumber = chip.documentNumber.map(_.value),
          dob = chip.dob.map(_.value),
          doe = chip.doe.map(_.value),
          surname = chip.surname.map(_.value),
          givenName = chip.givenName.map(_.value),
          gender = chip.gender.map(_.value),
          nationality = chip.nationality.map(_.value),
          activeAuth = chip.activeAuth.map(_.value),
          passiveAuth = chip.passiveAuth.map(_.value)
        )
        data.toSome
      case _ => None
    }

    EKYCDocumentVerification(
      provider = providerName,
      documentType = documentType,
      identityType = identityType,
      identityDoc = identityDoc,
      identityInfo = identityInfo,
      faceMatchingScore = faceMatchingScore,
      identityFiles = identityImages,
      faceImageFiles = faceImages,
      chipImageFiles = chipImages,
      identityRawFiles = identityRawImages,
      nextStep = nextStep,
      metadata = metadata,
      chipData = chipData
    )
  }

  private def downloadFaceImages(vbdDocument: JsonNode): Option[Seq[BytesFile]] = {
    var idx = -1
    val files = Seq("selfie_image", "face_to_compare_image").flatMap { faceKey =>
      val url = vbdDocument.at(s"/validation_info/face/$faceKey").asText("")
      idx += 1
      _downloadImages(idx, faceKey, url)
    }
    if (files.nonEmpty) Some(files) else None
  }

  private def downloadChipImages(vbdDocument: JsonNode): Option[Seq[BytesFile]] = {
    if (isEmptyNode(vbdDocument.at("/validation_info/chip"))) {
      None
    } else {
      var idx = -1
      val files = Seq("chip_image").flatMap { faceKey =>
        val url = vbdDocument.at(s"/validation_info/chip/$faceKey").asText("")
        idx += 1
        _downloadImages(idx, faceKey, url)
      }
      if (files.nonEmpty) Some(files) else None
    }
  }

  private def downloadIdentityImages(vbdDocument: JsonNode): Option[Seq[BytesFile]] = {
    var idx = -1
    val files = Seq("front_aligned_image", "back_aligned_image").flatMap { docKey =>
      val url = vbdDocument.at(s"/validation_info/ocr/$docKey").asText("")
      idx += 1
      _downloadImages(idx, docKey, url)
    }
    if (files.nonEmpty) Some(files) else None
  }

  private def downloadRawIdentityImages(vbdDocument: JsonNode): Option[Seq[BytesFile]] = {
    var idx = -1
    val files = Seq("front_raw_image", "back_raw_image").flatMap { docKey =>
      val url = vbdDocument.at(s"/validation_info/ocr/$docKey").asText("")
      idx += 1
      _downloadImages(idx, docKey, url)
    }
    if (files.nonEmpty) Some(files) else None
  }

  private def _downloadImages(idx: Int, key: String, url: String) = {
    if (url.nonEmpty) {
      val fileName = getUrlWithoutQueryParams(url).split("/").last
      val fileExt = _getFileExtension(fileName, key)
      val localFilePath = s"/tmp/${UUID.randomUUID()}/$key.$fileExt"
      CustomUtils.downloadPhoto(url, localFilePath)
      Some(BytesFile(UUID.randomUUID().toString, localFilePath, s"${System.currentTimeMillis()}_$key.$fileExt", s"image/$fileExt", Some(fileName), idx = idx))
    } else None
  }

  private def _getFileExtension(fileName: String, key: String): String = {
    fileName.split("\\.").lastOption match {
      case Some(ext) if Seq("jpg", "png", "pdf").contains(ext) => ext
      case _ => throw RCustomException("invalid_param", s"$key is not supported content-type")
    }
  }

  private def decryptResponseData(data: String, key: String): JsonNode = {
    try {
      val cipher = Cipher.getInstance("AES/GCM/NoPadding")
      val decodedBytes = Base64.getDecoder.decode(data)

      val ivSize = 12
      val iv = decodedBytes.slice(0, ivSize)
      val encryptedBytes = decodedBytes.slice(ivSize, decodedBytes.length)

      val secretKey = new SecretKeySpec(key.getBytes("UTF-8"), "AES")
      val gcmSpec = new GCMParameterSpec(128, iv)

      cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec)
      val decryptedBytes = cipher.doFinal(encryptedBytes)

      JsonHelper.readTree(new String(decryptedBytes, "UTF-8"))
    } catch {
      case e: Exception =>
        error(s"Failed to get document from VBD API: cannot decrypt response data - ${e.getMessage}")
        throw RCustomException("failed_to_get_document", "Failed to decrypt response data")
    }
  }

  private def checkDocumentValidationInfo(validationInfo: JsonNode): Boolean = {
    // test scenarios
    if (enableTestScenarios == "verify_document_validate_fail") {
      warn(s"Test scenario: verify_document_validate_fail")
      return false
    }

    // validate supported document type
    val ocrNode = validationInfo.at("/ocr")
    if (isEmptyNode(ocrNode) || isEmptyNode(ocrNode.at("/type"))) {
      error("Missing required /ocr/type node")
      return false
    }

    val docType = EKYCDocumentType.opt(ocrNode.at("/type").asText())
    docType match {
      case Some(value) =>
        if (!documentTypeSupported.contains(value.toString)) {
          error(s"Unsupported document type: ${value.toString}")
          return false
        }
      case _ =>
        error(s"Unknown document type: ${ocrNode.at("/type").asText()}")
        return false
    }

    // Validate required fields by steps
    val requiredSteps = _getVerifySteps(docType)
    requiredSteps.foreach { step: String =>
      val isValid = EKYCVerifyStep.opt(step) match {
        case Some(SCAN_FRONT_DOCUMENT) => !isEmptyNode(ocrNode.at("/front_aligned_image"))
        case Some(SCAN_BACK_DOCUMENT) => !isEmptyNode(ocrNode.at("/back_aligned_image"))
        case Some(SCAN_NFC) => !isEmptyNode(validationInfo.at("/chip"))
        case Some(SCAN_FACE) =>
          val isHaveFaceData = !isEmptyNode(validationInfo.at("/face"))
          val isValidFaceCompare = if (useFaceCompare) !isEmptyNode(validationInfo.at("/face/compare_data")) else true
          val isValidFaceLiveness = if (useFaceLiveness) !isEmptyNode(validationInfo.at("/face/liveness_data")) else true
          isHaveFaceData && isValidFaceCompare && isValidFaceLiveness
        case _ =>
          error(s"Unknown verify step: $step")
          throw NotFoundException(s"Unknown verify step: $step")
      }
      if (!isValid) {
        error(s"Missing data for required step: $step for document type: $docType")
        return false
      }
    }

    // check mrz_info for ID_CARD and CHIP_BASED_ID_CARD
    if (docType.contains(EKYCDocumentType.ID_CARD) || docType.contains(EKYCDocumentType.CHIP_BASED_ID_CARD)) {
      if (isEmptyNode(ocrNode.at("/mrz_info"))) {
        error(s"Missing required /ocr/mrz_info node for document type: $docType")
        return false
      }
    }

    if (validationInfo.at("/validation_summary").asInt(-1) != validationSummaryPassed) {
      error(s"Document validation summary is not passed")
      return false
    }
    true
  }

  private def _getVerifySteps(docType: Option[EKYCDocumentType]): Seq[String] = {
    docType match {
      case Some(EKYCDocumentType.ID_CARD) => idCardVerifySteps
      case Some(EKYCDocumentType.CHIP_BASED_ID_CARD) => chipBasedIdCardVerifySteps
      case Some(EKYCDocumentType.WHITE_ID_CARD) => whiteIdCardVerifySteps
      case Some(EKYCDocumentType.PASSPORT) => passportVerifySteps
      case Some(EKYCDocumentType.LEGACY_ID) => legacyIdVerifySteps
      case _ =>
        error(s"Unknown document type: $docType")
        throw NotFoundException(s"Unknown document type: $docType")
    }
  }

  private def compareDocumentData(vbdDocument: JsonNode, requestDocument: VBDDocumentInfo): Boolean = {
    // test scenarios
    if (enableTestScenarios == "verify_document_compare_fail") {
      warn(s"Test scenario: verify_document_compare_fail")
      return false
    }

    val vbdValidationInfo = vbdDocument.at("/validation_info")
    val requestValidationInfo = JsonHelper.readTree(JsonHelper.toJson(requestDocument.validationInfo))

    def compareNodes(sourceNode: JsonNode, targetNode: JsonNode, path: String): Boolean = {
      val fieldNames = sourceNode.fieldNames()
      var allMatch = true

      while (fieldNames.hasNext) {
        val fieldName = fieldNames.next()
        val childSourceNode = sourceNode.path(fieldName)
        val childTargetNode = targetNode.path(fieldName)
        val currentPath = s"$path/$fieldName"

        if (childTargetNode.isMissingNode) {
          // Skip comparison if the node is missing in target
        } else if (childSourceNode.isObject) {
          allMatch &= compareNodes(childSourceNode, childTargetNode, currentPath)
        } else if (isImageUrl(childSourceNode.asText())) {
          val url1 = getUrlWithoutQueryParams(childSourceNode.asText())
          val url2 = getUrlWithoutQueryParams(childTargetNode.asText())
          if (url1 != url2) {
            warn(s"Difference found at $currentPath: $url1 != $url2")
            allMatch = false
          }
        } else if (childSourceNode.isNumber && childTargetNode.isNumber) {
          val sourceValue = f"${childSourceNode.asDouble()}%.2f"
          val targetValue = f"${childTargetNode.asDouble()}%.2f"
          if (sourceValue != targetValue) {
            warn(s"Difference found at $currentPath: $sourceValue != $targetValue")
            allMatch = false
          }
        } else if (!childSourceNode.equals(childTargetNode)) {
          warn(s"Difference found at $currentPath: ${childSourceNode.toString} != ${childTargetNode.toString}")
          allMatch = false
        }
      }
      allMatch
    }

    compareNodes(vbdValidationInfo, requestValidationInfo, "/validation_info")
  }

  private def isImageUrl(url: String): Boolean = {
    // Regex to validate URL format
    val urlRegex = """^(https?|ftp)://[^\s/$.?#].[^\s]*$""".r
    // Common image file extensions
    val imageExtensions = List(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp")

    // Check if the URL matches the regex and ends with an image extension
    urlRegex.matches(url) && imageExtensions.exists(url.split("\\?")(0).toLowerCase.endsWith)
  }

  private def getUrlWithoutQueryParams(url: String): String = {
    val uri = new java.net.URI(url)
    new java.net.URI(uri.getScheme, uri.getAuthority, uri.getPath, null, null).toString
  }

  private def isEmptyNode(node: JsonNode): Boolean = {
    if (node.isMissingNode || node.isNull) {
      true
    } else if (node.isArray) {
      node.size() == 0
    } else if (node.isObject) {
      node.size() == 0
    } else if (node.isTextual) {
      node.asText().isEmpty
    } else {
      false
    }
  }
  override def feedback(documentId: String, requestId: String, feedback: Feedback, note: Option[String]): Future[FeedbackResponse] = Profiler(s"${getClass.getCanonicalName}.feedback") {
    val systemCustomerId = Constant.USER_SYSTEM

    // Step 1: Get access token with proper Future composition
    requestAccessToken(systemCustomerId, loadCache = true)
      .or(requestAccessToken(systemCustomerId))
      .flatMap {
        case Some(vbdSdkAuthInfo) =>
          vbdSdkAuthInfo.accessToken match {
            case Some(token) =>
              info(s"Feedback VBD document $documentId with requestId $requestId")

              // Step 2: Transform feedback data functionally
              val transformedFeedback = transformFeedbackFields(feedback)

              // Step 3: Build request
              val feedbackRequest = FeedbackRequest(
                requestId = requestId,
                appId = appId,
                feedback = transformedFeedback,
                note = note
              )

              // Step 4: Make HTTP request asynchronously
              makeHttpRequest(token, feedbackRequest)
                .map(parseResponse)
                .handle {
                  case e: Exception =>
                    error(s"Failed to send feedback for document $documentId: ${e.getMessage}", e)
                    FeedbackResponse(
                      success = Some(false),
                      message = Some(s"Request failed: ${e.getMessage}"),
                      requestId = Some(requestId)
                    )
                }

            case None =>
              Future.exception(RCustomException("invalid_vbd_access_token", "VBD access token is invalid"))
          }

        case None =>
          Future.exception(RCustomException("get_vbd_access_token_failed", "Get VBD access token failed"))
      }
  }

  /**
   * Transform feedback fields using functional approach to avoid concurrent modification
   */
  private def transformFeedbackFields(feedback: Feedback): Feedback = {
    feedback.ocrFeedback match {
      case Some(ocrFeedback) =>
        val transformedOcrFeedback = ocrFeedback.fieldAccuracy match {
          case Some(fields) if fields.nonEmpty =>
            val fieldMappings = Map(
              "no" -> "id_number",
              "expired_date" -> "doe",
              "issued_date" -> "doi",
              "issued_by" -> "poi",
              "dob" -> "date_of_birth"
            )

            val transformedFields = fields.map { case (field, accuracy) =>
              fieldMappings.getOrElse(field, field) -> accuracy
            }

            ocrFeedback.copy(fieldAccuracy = Some(transformedFields))

          case _ => ocrFeedback
        }

        feedback.copy(ocrFeedback = Some(transformedOcrFeedback))

      case None => feedback
    }
  }

  /**
   * Make HTTP request asynchronously using Future composition
   */
  private def makeHttpRequest(token: String, feedbackRequest: FeedbackRequest): Future[scalaj.http.HttpResponse[String]] = {
    async {
      info(s"Sending feedback request: ${JsonHelper.toJson(feedbackRequest)}")

      Http(feedbackUrl)
        .timeout(30000, 30000)
        .header("Authorization", s"Bearer $token")
        .header("Content-Type", "application/json")
        .postData(JsonHelper.toJson(feedbackRequest))
        .asString
    }.rescue {
      case e: Exception =>
        error(s"HTTP request failed: ${e.getMessage}", e)
        Future.exception(e)
    }
  }

  /**
   * Parse HTTP response into FeedbackResponse
   */
  private def parseResponse(response: scalaj.http.HttpResponse[String]): FeedbackResponse = {
    try {
      info(s"Feedback response: ${response.body}")

      val responseNode = JsonHelper.readTree(response.body)
      val code = responseNode.at("/code").asInt(-1)
      val message = responseNode.at("/message").asText("")
      val feedbackRequestId = responseNode.at("/request_id").asText("")

      response.code match {
        case 200 if code == 0 =>
          FeedbackResponse(
            requestId = if (feedbackRequestId.nonEmpty) Some(feedbackRequestId) else None,
            message = if (message.nonEmpty) Some(message) else None,
            success = Some(true)
          )

        case 200 =>
          FeedbackResponse(
            success = Some(false),
            message = if (message.nonEmpty) Some(message) else Some("Unknown error"),
            requestId = if (feedbackRequestId.nonEmpty) Some(feedbackRequestId) else None
          )

        case _ =>
          FeedbackResponse(
            success = Some(false),
            message = Some(s"HTTP ${response.code}: ${if (message.nonEmpty) message else "Request failed"}"),
            requestId = if (feedbackRequestId.nonEmpty) Some(feedbackRequestId) else None
          )
      }
    } catch {
      case e: JsonProcessingException =>
        error(s"Failed to parse response JSON: ${e.getMessage}", e)
        FeedbackResponse(
          success = Some(false),
          message = Some("Invalid response format"),
          requestId = None
        )
      case e: Exception =>
        error(s"Unexpected error parsing response: ${e.getMessage}", e)
        FeedbackResponse(
          success = Some(false),
          message = Some(s"Parse error: ${e.getMessage}"),
          requestId = None
        )
    }
  }
}

case class VbdSdkAuthInfo(
                           accessToken: Option[String],
                           refreshToken: Option[String],
                           username: Option[String],
                           @JsonDeserialize(contentAs = classOf[java.lang.Long])
                           expireIn: Option[Long],
                           @JsonDeserialize(contentAs = classOf[java.lang.Long])
                           serverTime: Option[Long],
                           requestId: Option[String],

                           // config
                           var additionalConfigs: Option[Map[String, Any]] = None
                         ) {
}

case class VBDDocumentInfo(
                            documentId: String,
                            faceId: Option[String],
                            validationInfo: ValidationInfo,
                            qualityWarnings: Option[Seq[QualityWarning]]
                          ) {
}

case class ValidationInfo(
                           face: Option[Face],
                           ocr: Option[Ocr],
                           chip: Option[Chip],
                           @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                           validationSummary: Int
                         )

case class Face(
                 compareData: Option[CompareData],
                 livenessData: Option[LivenessData],
                 faceToCompareImage: Option[String],
                 selfieImage: Option[String]
               )

case class CompareData(
                        ageGenderValidation: Option[AgeGenderValidation],
                        personCardValidation: Option[PersonValidation],
                        personChipValidation: Option[PersonValidation]
                      )

case class LivenessData(
                         @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                         mode: Option[Int],
                         @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                         numFaceDetected: Option[Int],
                         @JsonDeserialize(contentAs = classOf[java.lang.Double])
                         faceRatio: Option[Double],
                         @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                         isLiveness: Option[Boolean],
                         @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                         isSamePerson: Option[Boolean],
                         @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                         isBadQuality: Option[Boolean]
                       )

case class AgeGenderValidation(
                                inputGender: Option[String],
                                genderSelfiePredict: Option[String],
                                genderIdCardPredict: Option[String],
                                genderValidation: Option[String],
                                @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                inputAge: Option[Int],
                                @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                exactAge: Option[Int]
                              )

case class PersonValidation(
                             idValidation: Option[String],
                             @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                             isSamePerson: Option[Boolean],
                             @JsonDeserialize(contentAs = classOf[java.lang.Double])
                             similarityPercentage: Option[Double]
                           )

case class Ocr(
                backAlignedImage: Option[String],
                frontAlignedImage: Option[String],
                photoFromDocument: Option[String],
                frontRawImage: Option[String],
                backRawImage: Option[String],
                address: Option[ExtractValue],
                dob: Option[ExtractValue],
                doe: Option[ExtractValue],
                doi: Option[ExtractValue],
                gender: Option[ExtractValue],
                idNumber: Option[ExtractValue],
                mrzInfo: Option[MrzInfo],
                name: Option[ExtractValue],
                nationality: Option[ExtractValue],
                pob: Option[ExtractValue],
                hometown: Option[ExtractValue],
                poi: Option[ExtractValue],
                city: Option[ExtractValue],
                `type`: String,
                features: Option[ExtractValue]
              )

case class MrzInfo(
                    code: Option[ExtractValue],
                    dob: Option[ExtractValue],
                    documentNumber: Option[ExtractValue],
                    doe: Option[ExtractValue],
                    gender: Option[ExtractValue],
                    idNumber: Option[ExtractValue],
                    name: Option[ExtractValue],
                    nationality: Option[ExtractValue]
                  )

case class Chip(
                 @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                 dataMismatch: Option[Boolean],
                 documentType: Option[ExtractValue],
                 documentNumber: Option[ExtractValue],
                 idNumber: Option[ExtractValue],
                 surname: Option[ExtractValue],
                 givenName: Option[ExtractValue],
                 dob: Option[ExtractValue],
                 gender: Option[ExtractValue],
                 nationality: Option[ExtractValue],
                 doe: Option[ExtractValue],
                 doi: Option[ExtractValue],
                 @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                 activeAuth: Option[Boolean],
                 @JsonDeserialize(contentAs = classOf[java.lang.Boolean])
                 passiveAuth: Option[Boolean],
                 chipImage: Option[String]
               )

case class ExtractValue(
                         value: String,
                         warning: Option[String]
                       )

case class QualityWarning(
                           message: Option[String],
                           code: Option[String]
                         )

case class FieldAccuracy(
                          isCorrect: Option[Boolean] = None,
                          correctValue: Option[String] = None
                        )

case class OcrFeedback(
                        documentTypeCorrect: Option[Boolean] = None,
                        correctDocumentType: Option[String] = None,
                        fieldAccuracy: Option[Map[String, FieldAccuracy]] = None
                      )

case class BiometricValidation(
                                ageValidationCorrect: Option[Boolean] = None,
                                genderValidationCorrect: Option[Boolean] = None,
                                correctAge: Option[Int] = None,
                                correctGender: Option[String] = None
                              )
case class FaceFeedback(
                         livenessCorrect: Option[Boolean] = None,
                         comparisonCorrect: Option[Boolean] = None,
                         biometricValidation: Option[BiometricValidation] = None
                       )
case class Assessment(
                       isCorrect: Option[Boolean] = None,
                       actualQuality: Option[String] = None, // "good", "poor", "acceptable"
                       issueDescription: Option[String] = None
                     )

case class QualityFeedback(
                            glareAssessment: Option[Assessment] = None,
                            recaptureAssessment: Option[Assessment] = None,
                            shapeAssessment: Option[Assessment] = None
                          )

case class Feedback(
                     ocrFeedback: Option[OcrFeedback] = None,
                     faceFeedback: Option[FaceFeedback] = None,
                     qualityFeedback: Option[QualityFeedback] = None
                   )

case class FeedbackRequest(
                             requestId: String,
                             appId: String,
                             feedback: Feedback,
                             note: Option[String] = None
                           )

case class FeedbackResponse(
                             success: Option[Boolean] = None,
                             message: Option[String] = None,
                             requestId: Option[String] = None
                           )
