package profile.service

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JsonNode
import com.twitter.inject.Logging
import com.twitter.util.Future
import com.typesafe.config.Config
import profile.domain.customer.{CustomerIdentityDocType, EKYCDocumentType}
import profile.domain.customer.CustomerIdentityDocType.CustomerIdentityDocType
import profile.util.CTimeUtils
import scalaj.http.Http
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{RCustomException, UnsupportedException}
import vn.vhm.common.util.JsonHelper

import scala.jdk.CollectionConverters.{asScalaIteratorConverter, iterableAsScalaIterableConverter}

/**
 * <AUTHOR> 9/19/24 05:14
 */
trait AiIntegrationService {

  def extractIdentify(identityType: CustomerIdentityDocType, urls: Seq[String]): Future[Option[CustomerIdentityDocumentInfo]]

  def faceMatching(identityUrls: Seq[String], faceImageUrl: String): Future[Option[(String, Double)]]

  def extractAddress(addresses: Seq[String]): Option[Seq[DetailedAddress]]
}

case class AiIntegrationServiceImpl(aiConfig: Config) extends AiIntegrationService with Logging {

  private val extractIdentifyUrl = aiConfig.getString("extract_identify_url")
  private val matchingFacesUrl = aiConfig.getString("matching_faces_url")
  private val extractAddressUrl = aiConfig.getString("extract_address_url")
  private val identifyNoDocumentTypes = Seq(0, 1, 2, 3, 4, 5, 6, 23, 24)
  private val passportDocumentTypes = Seq(8, 9)
  private val confidenceScoreThreshold = 0.7

  override def extractIdentify(identityType: CustomerIdentityDocType, urls: Seq[String]): Future[Option[CustomerIdentityDocumentInfo]] = Profiler(s"${getClass.getCanonicalName}.extractIdentify") {
    async {

      val paramMap = Map("image_urls" -> urls, "score_threshold" -> 0.7)

      val resp = Http(extractIdentifyUrl)
        .timeout(30000, 30000)
        .header("Content-Type", "application/json")
        .postData(JsonHelper.toJson(paramMap))
        .asString

      info(s"extractIdentify($identityType, ${JsonHelper.toJson(urls)})\t${resp.code}\t${resp.body}")

      resp.code match {
        case 200 =>
          try {
            val jsonNode = JsonHelper.readTree(resp.body)

            jsonNode.at("/results/0/text_entities/id_number/value").asText("") match {
              case "" =>
                error(s"Failed when extract identity: ${resp.code} - ${resp.body}\t${urls.mkString(",")}")
                None
              case idNo =>
                jsonNode.path("results").asScala
                  .foldLeft(CustomerIdentityDocumentInfo(identityType = identityType.toString, no = idNo)) { (holder, itemNode) =>
                    parseDoc(identityType, itemNode, holder)
                  }
                  .toSome
            }
          } catch {
            case e: JsonProcessingException =>
              error(s"Failed when extract identity($identityType, ${urls.mkString(",")}): ${resp.code} - ${resp.body}: ${e.getMessage}")
              None
            case e: Exception =>
              error(s"Failed when extract identity($identityType, ${urls.mkString(",")}): ${resp.code} - ${resp.body}: ${e.getMessage}")
              throw e
          }
        case _ =>
          error(s"Failed when extract identity: ${resp.code} - ${resp.body}\t${urls.mkString(",")}")
          None
      }

    }

  }

  private def parseDoc(identityType: CustomerIdentityDocType, itemNode: JsonNode, holder: CustomerIdentityDocumentInfo): CustomerIdentityDocumentInfo = {

    val docType = itemNode.path("document_type").asInt(-1)

    identityType match {

      case CustomerIdentityDocType.CCCD if !identifyNoDocumentTypes.contains(docType) =>
        throw RCustomException("cannot_parse_identity_document_data", "Invalid customer identify file data")

      case CustomerIdentityDocType.CCCD =>

      case CustomerIdentityDocType.PASSPORT if !passportDocumentTypes.contains(docType) =>
        throw RCustomException("cannot_parse_identity_document_data", "Invalid customer identify file data")

      case CustomerIdentityDocType.PASSPORT =>

      case _ => throw UnsupportedException(s"unsupported identity type ${identityType.toString}")
    }

    val documentType = docType match {
      case 6 | 4 => EKYCDocumentType.WHITE_ID_CARD
      case 0 | 1 => EKYCDocumentType.CHIP_BASED_ID_CARD
      case 23 | 24 => EKYCDocumentType.ID_CARD
      case 3 | 5 | 2 | 4 => EKYCDocumentType.LEGACY_ID
      case 8 | 9 => EKYCDocumentType.PASSPORT
      case _ => throw UnsupportedException(s"unsupported document type $docType")
    }

    holder.copy(
      fullName = holder.fullName.orElse(itemNode.at("/text_entities/name/value").asText("").toSome.filter(_.nonEmpty)),
      dob = holder.dob.orElse(CTimeUtils.getValidDate(itemNode.at("/text_entities/dob/value").asText("")).filter(_.nonEmpty)),
      gender = holder.gender.orElse(itemNode.at("/text_entities/gender/value").asText("").toSome.filter(_.nonEmpty)),

      expiredDate = holder.expiredDate.orElse(Some(itemNode.at("/text_entities/doe/value").asText("")).filter(_.nonEmpty)),

      hometown = holder.hometown.orElse(itemNode.at("/text_entities/hometown/value").asText("").toSome.filter(_.nonEmpty)),
      address = holder.address.orElse(itemNode.at("/text_entities/address/value").asText("").toSome.filter(_.nonEmpty)),
      region = holder.region.orElse(itemNode.at("/text_entities/region/value").asText("").toSome.filter(_.nonEmpty)),
      city = holder.city.orElse(itemNode.at("/text_entities/city/value").asText("").toSome.filter(_.nonEmpty)),

      nationality = holder.nationality.orElse(itemNode.at("/text_entities/nationality/value").asText("").toSome.filter(_.nonEmpty)),

      issuedBy = holder.issuedBy.orElse(itemNode.at("/text_entities/poi/value").asText("").toSome.filter(_.nonEmpty)),
      issuedDate = holder.issuedDate.orElse(CTimeUtils.getValidDate(itemNode.at("/text_entities/doi/value").asText("")).filter(_.nonEmpty)),
      documentType = documentType.toString.toSome,
      mrzId = holder.mrzId.orElse(itemNode.at("/text_entities/mrz_id/value").asText("").toSome.filter(_.nonEmpty))
    )
  }

  override def faceMatching(identityUrls: Seq[String], faceImageUrl: String): Future[Option[(String, Double)]] = Profiler(s"${getClass.getCanonicalName}.matchingFace") {
    async {
      val paramMap = Map(
        "front_image_url" -> identityUrls.head,
        "back_image_url" -> identityUrls.last,
        "selfie_image_url" -> faceImageUrl,
        "score_threshold" -> 0.0,
        "check_real_selfie" -> true
      )

      val resp = Http(matchingFacesUrl)
        .timeout(30000, 30000)
        .header("Content-Type", "application/json")
        .postData(JsonHelper.toJson(paramMap))
        .asString

      info(s"matchingFace(${JsonHelper.toJson(identityUrls)}, $faceImageUrl)\t${resp.code}\t${resp.body}")

      resp.code match {
        case 200 =>
          try {
            val jsonNode = JsonHelper.readTree(resp.body)

            val score = jsonNode.at("/facial_matching/score").asDouble(0.0d)

            val result = jsonNode.at("/facial_matching/label_id").asInt(0) match {
              case 1 => ("matched", score)
              case 2 | 3 | 4 => ("unmatched", score)
              case 5 | 6 => ("invalid", score)
              case _ => ("", score)
            }
            result.toSome

          } catch {
            case e: JsonProcessingException =>
              error(s"Failed when matchingFace(${identityUrls.mkString(",")}, $faceImageUrl): ${resp.code} - ${resp.body}: ${e.getMessage}")
              None
            case e: Exception =>
              error(s"Failed when matchingFace(${identityUrls.mkString(",")}, $faceImageUrl): ${resp.code} - ${resp.body}: ${e.getMessage}")
              throw e
          }
        case _ =>
          error(s"Failed when matchingFace: ${resp.code} - ${resp.body}\t${identityUrls.mkString(",")}\t$faceImageUrl")
          None
      }
    } rescue {
      case e: Exception => async {
        error(s"Failed when matchingFace: ${identityUrls.mkString(",")} - $faceImageUrl", e)
        None
      }
    }
  }

  override def extractAddress(addresses: Seq[String]): Option[Seq[DetailedAddress]] = Profiler(s"${getClass.getCanonicalName}.extractAddress") {
    val paramMap = Map(
      "addresses" -> addresses,
      "score_threshold" -> confidenceScoreThreshold
    )

    val resp = Http(extractAddressUrl)
      .timeout(30000, 30000)
      .header("Content-Type", "application/json")
      .postData(JsonHelper.toJson(paramMap))
      .asString

    info(s"extractAddress(${addresses.mkString(" | ")})\t${resp.code}\t${resp.body}")

    resp.code match {
      case 200 =>
        try {
          val jsonNode = JsonHelper.readTree(resp.body)
          val detailedAddresses = jsonNode.path("detailed_addresses").elements().asScala.toSeq.map { node =>
            DetailedAddress(
              nationality = Option(node.path("nationality").asText()).filter(_.nonEmpty),
              region = Option(node.path("region").asText()).filter(_.nonEmpty),
              province = Option(node.path("province").asText()).filter(_.nonEmpty),
              district = Option(node.path("district").asText()).filter(_.nonEmpty),
              ward = Option(node.path("ward").asText()).filter(_.nonEmpty),
              street = Option(node.path("street").asText()).filter(_.nonEmpty)
            )
          }

          detailedAddresses.toSome
        } catch {
          case e: Exception =>
            error(s"Failed when extractAddress(${addresses.mkString(",")}): ${resp.code} - ${resp.body}: ${e.getMessage}")
            None
        }
      case _ =>
        error(s"Failed when matchingFace: ${resp.code} - ${resp.body}\t${addresses.mkString(",")}")
        None
    }
  }
}

case class CustomerIdentityDocumentInfo(
                                         identityType: String,
                                         no: String,
                                         var fullName: Option[String] = None,
                                         var dob: Option[String] = None,
                                         var gender: Option[String] = None,

                                         var issuedBy: Option[String] = None, // Nơi cấp
                                         var issuedDate: Option[String] = None, // Ngày cấp
                                         var expiredDate: Option[String] = None, // Ngày hết hạn

                                         var hometown: Option[String] = None,
                                         var address: Option[String] = None,
                                         var ocrAddress: Option[String] = None,
                                         var region: Option[String] = None,
                                         var city: Option[String] = None,

                                         var nationality: Option[String] = None,
                                         var mrzId: Option[String] = None,
                                         var documentType: Option[String] = None
                                       ) {

  @JsonIgnore
  def isComplete: Boolean = {
    fullName.isDefined &&
      dob.isDefined && gender.isDefined &&
      nationality.isDefined && address.isDefined &&
      issuedDate.isDefined && expiredDate.isDefined
  }

}

case class DetailedAddress(
                            nationality: Option[String] = None,
                            region: Option[String] = None,
                            province: Option[String] = None,
                            district: Option[String] = None,
                            ward: Option[String] = None,
                            street: Option[String] = None,
                          ) {

}