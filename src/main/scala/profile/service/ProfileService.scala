package profile.service

import com.fasterxml.jackson.databind.node.ObjectNode
import com.twitter.inject.Logging
import com.twitter.util.{Future, Promise, Return, Throw}
import profile.domain.customer.CustomerIdentityVerifyStatus.CustomerIdentityVerifyStatus
import profile.domain.customer._
import profile.domain.event._
import profile.domain.request.UpdateUserRequest
import profile.domain.response.CustomerDTO
import profile.domain.{BytesFile, UserStatus}
import profile.exception.{AlreadyExistByNormalizedEmailException, AlreadyExistEmailException, AlreadyExistPhoneException}
import profile.service.files.{CustomerFileService, ImgProxyService}
import profile.util.CTimeUtils.DatePart
import profile.util.Constant.futurePoolLock
import profile.util.{AddressHelper, CTimeUtils, Constant, CustomUtils, EmailHelper}
import vn.vhm.common.client.kafka_010.StringKafkaProducer
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{NotFoundException, RCustomException, UnsupportedException, VhmCustomException}
import vn.vhm.common.repository.lock.AsyncLockManager
import vn.vhm.common.util.{JsonHelper, JsonHelperAllowNull}

import java.io.File
import java.sql.Connection
import java.util.UUID
import javax.inject.Inject
import scala.concurrent.duration.DurationInt

/**
 * Created by sonpn.
 */

trait ProfileService {

  def checkEmailExist(email: String): Future[Boolean]

  def addUser(user: Customer): Future[Customer]

  def updateUser(username: String, updateReq: UpdateUserRequest): Future[Customer]

  def internalUpdateUser(username: String, user: Customer,
                         oldUserOpt: Option[Customer] = None,
                         setUserToPnlCustomers: Seq[Long] = Nil, removeUserFromPnlCustomers: Seq[Long] = Nil): Future[Customer]

  //  def internalUpdateProfile(username: String, profile: UserProfile): Future[Unit]

  def opt(username: String): Future[Option[Customer]]

  def get(username: String, rescanPnl: Boolean = false): Future[Customer]

  def get(customer: Customer, rescanPnl: Boolean): Future[Customer]

  def getActiveUsernameByPhone(normPhone: String): Future[Option[String]]

  def existActiveUserByPhone(normPhone: String): Future[Boolean]

  def getActiveUserByPhone(normPhone: String): Future[Option[Customer]]

  def getActiveUsernameByEmail(normPhone: String): Future[Option[String]]

  def existActiveUserByEmail(email: String): Future[Boolean]

  def getActiveUserByEmail(email: String, withNormalize: Boolean = false): Future[Option[Customer]]

  def getActiveUserByNormalizedEmail(normalizedEmail: String): Future[Option[Customer]]

  def getActiveUserByPhoneOrEmail(normPhone: Option[String], email: Option[String], rescanPnl: Boolean): Future[Customer]

  def verifyUpdateEmail(customer: Customer, email: String, user: Option[Customer]): Future[Unit]

  def verifyUpdatePhone(customer: Customer, phone: String, user: Option[Customer]): Future[Unit]

  def updateEmail(username: String, email: String, verified: Boolean): Future[Unit]

  def updatePhone(username: String, phone: String, verified: Boolean): Future[Unit]

  def updateRankingProgress(username: String, pnlRankingProgress: CustomerPnlRankingProgress, oldUserOpt: Option[Customer]): Future[Unit]

  def updateAvatar(username: String, avatarUrl: String): Future[Unit]

  def deleteUser(username: String,
                 auditor: Option[String] = None,
                 manualRequestBy: Option[String] = None,
                 manualRequestReason: Option[String] = None
                ): Future[Unit]

  def getPnlMappings(username: String, forceFetch: Boolean, reMappingPnls: Seq[String], auditor: Option[String] = None, manualRequestBy: Option[String] = None, manualRequestReason: Option[String] = None): Future[CustomerPnlMapping]

  def getPnlMappings(customer: Customer, forceFetch: Boolean, reMappingPnls: Seq[String], auditor: Option[String], manualRequestBy: Option[String], manualRequestReason: Option[String]): Future[CustomerPnlMapping]

  def confirmMappingToPnl(username: String): Future[CustomerPnlMapping]

  def searchActiveUserId(phone: Seq[String], email: Seq[String], identityNumbers: Seq[String]): Future[Seq[String]]

  def reInitPnlMapping(userId: String,
                       auditor: Option[String] = None,
                       manualRequestBy: Option[String] = None,
                       manualRequestReason: Option[String] = None): Future[CustomerPnlMapping]

  def reInitPnlMapping(customer: Customer,
                       auditor: Option[String],
                       manualRequestBy: Option[String],
                       manualRequestReason: Option[String]): Future[CustomerPnlMapping]

  def verifyIdentity(
                      customerId: String,
                      identityDoc: CustomerIdentityDocument,
                      identityFilesReq: Seq[BytesFile], faceImageFile: Option[BytesFile],
                      stageMetadata: Option[String], customAddress: Option[String]
                    ): Future[CustomerIdentityDocument]

  def verifyIdentityByProvider(
                                customerId: String,
                                documentInfo: EKYCDocumentVerification,
                                sendAutoApproval: Boolean = false
                              ): Future[CustomerIdentityDocument]

  def updateIdentifyVerifyStatus(
                                  customerId: String,
                                  verifyStatus: CustomerIdentityVerifyStatus, verifyStatusMessage: String,
                                  identityType: String, identityDocumentVerifyId: Long
                                ): Future[Unit]

  def canVerifyIdentityDocument(customerId: String): Future[Unit]

  def extractDemographicDataByVerifiedIdentity(customerIdentityVerifiedEvent: CustomerIdentityVerifiedEvent): Future[Unit]

  def removeDemographicDataByRejectedIdentity(customerIdentityRejectedEvent: CustomerIdentityRejectedEvent): Future[Unit]

  def updateCdpProfileDemographic(username: String, customerCdpProfile: CustomerCdpProfile, oldUserOpt: Option[Customer]): Future[Unit]

  /**
   * Normalize emails for customers that have an email but no normalized_email within a specified ID range
   *
   * @param batchSize Number of customers to process in each batch
   * @param fromId    Start of customer ID range (inclusive, -1 means no lower limit)
   * @param toId      End of customer ID range (inclusive, -1 means no upper limit)
   * @return Number of customers processed
   */
  def normalizeEmailsInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int]

  def handleCustomerNewSignedInEvent(event: CustomerNewSignedInEvent): Future[Unit]

  def syncLoginTimeInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int]

  /**
   * Migrate missing identity verification data (documentType, mrzId, city) for customers
   * with approval_status='SUCCESS' within a specified ID range
   *
   * @param batchSize Number of records to process in each batch
   * @param fromId    Start of ID range (inclusive, -1 means no lower limit)
   * @param toId      End of ID range (inclusive, -1 means no upper limit)
   * @return Number of records processed
   */
  def migrateIdentityVerificationDataInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int]

}

class ProfileServiceImpl @Inject()(
                                    customerDAO: CustomerDAO,
                                    pnlCustomerDAO: PnlCustomerDAO,

                                    internalService: InternalService,

                                    kafkaProducer: StringKafkaProducer,
                                    customerHistoricalTopic: String,
                                    customerTierChangedTopic: String,
                                    autoApproveIdentityVerificationTopic: String,

                                    createCustomerLock: AsyncLockManager,
                                    updateCustomerLock: AsyncLockManager,
                                    createOrUpdatePhoneLock: AsyncLockManager,
                                    createOrUpdateEmailLock: AsyncLockManager,
                                    internalBatchProcessLock: AsyncLockManager,

                                    imgProxyService: ImgProxyService,
                                    aiIntegrationService: AiIntegrationService,
                                    vbdEyePassIntegrationService: VbdEyePassIntegrationService,
                                    customerFileService: CustomerFileService,
                                    identityVerificationDAO: CustomerIdentityVerificationDAO,

                                    contextHolder: ContextHolder,
                                  ) extends ProfileService with Logging {

  protected val clazz = getClass.getCanonicalName

  def opt(username: String): Future[Option[Customer]] = async {
    customerDAO.select(username)
  }

  private def internalGet(username: String): Future[Customer] = Profiler(s"$clazz.internalGet") {
    opt(username).map(_.getOrElse(throw NotFoundException(s"Not found user $username")))
  }

  def get(username: String, rescanPnl: Boolean): Future[Customer] = Profiler(s"$clazz.get") {
    internalGet(username).flatMap(get(_, rescanPnl))
    //      .onSuccess(customer => makeAcceptConsent(customer, System.currentTimeMillis()))
  }

  override def get(customer: Customer, rescanPnl: Boolean): Future[Customer] = Profiler(s"$clazz.get_Obj") {
    async {
      customer
    }
      .flatMap(enhanceProfileInfo)
      .onSuccess(customer => if (rescanPnl) getPnlMappings(customer, forceFetch = false, reMappingPnls = Nil, auditor = None, manualRequestBy = None, manualRequestReason = None))
  }


  private def enhanceProfileInfo(customer: Customer): Future[Customer] = {
    for {
      mapReasonValues <- internalService.getMasterDataIdentityRejectReason()
      _ = {
        val lang = contextHolder.getLang()
          .orElse(customer.displaySetting.flatMap(_.language).flatMap(CustomUtils.getLang))
          .getOrElse(Constant.DEFAULT_LANGUAGE)

        customer.listIdentityDocument.getOrElse(Nil)
          .filter(_.verifiedStatus.contains(CustomerIdentityVerifyStatus.REJECTED.toString))
          .foreach(item => {
            item.verifiedStatusMessage.getOrElse("").split(":", -1) match {
              case split if split.size >= 2 =>
                val v = mapReasonValues.get(split.head).flatMap(m => m.get(lang).filter(_.nonEmpty).orElse(m.get(Constant.DEFAULT_LANGUAGE)))
                item.verifiedStatusMessageDisplay = v
                item.verifiedStatusMessageNote = split.tail.mkString(": ").toSome
              case split if split.size == 1 =>
                val v = mapReasonValues.get(split.head).flatMap(m => m.get(lang).orElse(m.get(Constant.DEFAULT_LANGUAGE)))
                item.verifiedStatusMessageDisplay = v
                item.verifiedStatusMessageNote = None
              case _ =>
            }
          })
      }

      _ <- {
        if (customer.segments.exists(_.nonEmpty) && customer.segmentCodes.isEmpty) {
          internalService.mgetSegmentCodes(customer.segments.get).map(mapSegmentCodes => {
            val validCodes = mapSegmentCodes.filter(_._2.nonEmpty)
            customer.segmentCodes = validCodes.values.toSeq.toSome.filter(_.nonEmpty)
            customer.segments = validCodes.keys.toSeq.toSome
          })

          Future.value(customer)
        }
        else Future.Unit
      }

    } yield customer

  }

  override def getActiveUsernameByPhone(normPhone: String): Future[Option[String]] = Profiler(s"$clazz.getActiveUsernameByPhone") {
    async {
      customerDAO.selectUsernameByPhone(normPhone, status = UserStatus.Active)
    }
  }

  override def existActiveUserByPhone(normPhone: String): Future[Boolean] = Profiler(s"$clazz.existActiveUserByPhone") {
    async {
      customerDAO.existUserByPhone(normPhone, UserStatus.Active)
    }
  }

  override def getActiveUserByPhone(normPhone: String): Future[Option[Customer]] = Profiler(s"$clazz.getActiveUserByPhone") {
    async {
      customerDAO.selectUserByPhone(normPhone, status = UserStatus.Active)
    }
  }

  override def getActiveUsernameByEmail(email: String): Future[Option[String]] = Profiler(s"$clazz.getActiveUsernameByEmail") {
    async {
      customerDAO.selectUsernameByEmail(email, status = UserStatus.Active)
    }
  }

  override def existActiveUserByEmail(email: String): Future[Boolean] = Profiler(s"$clazz.existActiveUserByEmail") {
    async {
      customerDAO.existUserByEmail(email, status = UserStatus.Active)
    }
  }

  override def getActiveUserByEmail(email: String, withNormalize: Boolean = false): Future[Option[Customer]] = Profiler(s"$clazz.getActiveUserByEmail") {
    async {
      customerDAO.selectUserByEmail(email, status = UserStatus.Active, withNormalize)
    }
  }

  override def getActiveUserByNormalizedEmail(normalizedEmail: String): Future[Option[Customer]] = Profiler(s"$clazz.getActiveUserByNormalizedEmail") {
    async {
      customerDAO.selectUserByNormalizedEmail(normalizedEmail, status = UserStatus.Active)
    }
  }

  override def getActiveUserByPhoneOrEmail(normPhone: Option[String], email: Option[String], rescanPnl: Boolean): Future[Customer] = Profiler(s"$clazz.getActiveUserWithPhoneOrEmail") {
    async {
      customerDAO.selectUserByPhoneOrEmail(normPhone, email, status = UserStatus.Active)
    }
      .map(_.getOrElse(throw NotFoundException(s"Not found user by phone or email")))
      .flatMap(get(_, rescanPnl))
  }

  //region Update & Verify Email & Phone

  override def internalUpdateUser(username: String, user: Customer,
                                  oldUserOpt: Option[Customer] = None,
                                  setUserToPnlCustomers: Seq[Long] = Nil,
                                  removeUserFromPnlCustomers: Seq[Long] = Nil
                                 ): Future[Customer] = Profiler(s"$clazz.internalUpdateUser") {
    for {
      oldUser <- oldUserOpt.fold(get(username, rescanPnl = false))(Future.value)
      newUser = {
        val currTime = System.currentTimeMillis()
        user.updatedTime = currTime.toSome
        user.userId = username.toSome

        if (setUserToPnlCustomers.nonEmpty || removeUserFromPnlCustomers.nonEmpty) {
          customerDAO.executeTransaction(customerDAO.ds)(conn => {

            customerDAO.update(user, connection = Some(conn))

            if (setUserToPnlCustomers.nonEmpty) setUserToPnlCustomers.foreach(pnlCustomerId => {
              pnlCustomerDAO.update(
                PnlCustomer(id = pnlCustomerId.toSome, updatedTime = currTime.toSome, vclubUserId = username.toLong.toSome),
                connection = Some(conn)
              )
            })

            if (removeUserFromPnlCustomers.nonEmpty) removeUserFromPnlCustomers.foreach(pnlCustomerId => {
              pnlCustomerDAO.update(
                PnlCustomer(id = pnlCustomerId.toSome, updatedTime = currTime.toSome, vclubUserId = 0L.toSome),
                connection = Some(conn)
              )
            })

          })
        }
        else customerDAO.update(user)
        user
      }
      _ = logHistorical(HistoricalActionEvent.UPDATED.toString, username, oldUser.toSome, oldUser.copy().mergeWith(newUser).toSome)
      _ <- checkAndSendDemographicMetadataChangedEvent(oldUser, oldUser.copy().mergeWith(newUser))
    } yield newUser
  }.onFailure(ex => {
    error(s"Failed when internalUpdateUser($username, ${JsonHelper.toJson(user)}, ${JsonHelper.toJson(setUserToPnlCustomers)}, ${JsonHelper.toJson(oldUserOpt)})", ex)
  })

  override def verifyUpdateEmail(customer: Customer, email: String, optCurrUser: Option[Customer]): Future[Unit] = Profiler(s"$clazz.verifyUpdateEmail") {
    val normalizedEmail = EmailHelper.normalize(email)
    for {
      _ <- if (customer.email.exists(_.nonEmpty) && customer.emailVerified.contains(1)) {
        Future.exception(RCustomException("already_exist_user_email", s"User Email is already exist"))
      } else Future.Unit
      _ <- createOrUpdateEmailLock.withLock(normalizedEmail) {
        futurePoolLock {
          if (customerDAO.existUserByEmail(email, UserStatus.Active, excludeUser = customer.userId))
            throw AlreadyExistEmailException(s"Email $email is already exist")
          if (customerDAO.existUserByNormalizedEmail(normalizedEmail, UserStatus.Active, excludeUser = customer.userId))
            throw AlreadyExistByNormalizedEmailException(email, data = Map("full_name" -> customer.fullName, "normalized_email" -> normalizedEmail, "existed_email" -> customer.email.get))
        }
      }
    } yield {}
  }

  override def verifyUpdatePhone(customer: Customer, phone: String, user: Option[Customer]): Future[Unit] = Profiler(s"$clazz.verifyUpdatePhone") {
    for {
      _ <- if (customer.phone.exists(_.nonEmpty) && customer.phoneVerified.contains(1)) {
        Future.exception(RCustomException("already_exist_user_phone", s"User Phone is already exist"))
      } else Future.Unit
      _ <- createOrUpdatePhoneLock.withLock(phone) {
        futurePoolLock {
          if (customerDAO.existUserByPhone(phone, UserStatus.Active, excludeUser = customer.userId))
            throw AlreadyExistPhoneException(s"Phone $phone is already exist")
        }
      }
    } yield {}
  }

  override def updateEmail(username: String, email: String, verified: Boolean): Future[Unit] = Profiler(s"$clazz.updateEmail") {
    for {
      currUser <- internalGet(username)
      _ <- verifyUpdateEmail(currUser, email, None)
      _ <- {
        val verifiedAsInt = if (verified) 1 else 0
        currUser.email match {
          case None =>
            internalUpdateUser(username, Customer(email = email.toSome, emailVerified = verifiedAsInt.toSome, normalizedEmail = EmailHelper.normalize(email).toSome), oldUserOpt = currUser.toSome)
          case Some(email) if email.contains(email) && currUser.emailVerified.contains(verifiedAsInt) => Future.Unit
          case Some(email) if email.contains(email) =>
            internalUpdateUser(username, Customer(emailVerified = verifiedAsInt.toSome), oldUserOpt = currUser.toSome)
          case _ =>
            Future.exception(UnsupportedException("Not support update email"))
        }
      }
    } yield {}
  }

  override def updatePhone(username: String, normPhone: String, verified: Boolean): Future[Unit] = Profiler(s"$clazz.updatePhone") {
    updateCustomerLock.withLock(username) {
      for {
        customer <- internalGet(username)
        _ <- verifyUpdatePhone(customer, normPhone, None)
        _ <- {
          val verifiedAsInt = if (verified) 1 else 0
          customer.phone match {
            case None =>
              internalUpdateUser(username, Customer(phone = normPhone.toSome, phoneVerified = verifiedAsInt.toSome), oldUserOpt = customer.toSome)
            case Some(phone) if phone.contains(normPhone) && customer.phoneVerified.contains(verifiedAsInt) => Future.Unit
            case Some(phone) if phone.contains(normPhone) =>
              internalUpdateUser(username, Customer(phoneVerified = verifiedAsInt.toSome), oldUserOpt = customer.toSome)
            case _ =>
              Future.exception(UnsupportedException("Not support update phone"))
          }
        }
      } yield {}
    }
  }

  override def checkEmailExist(email: String): Future[Boolean] = Profiler(s"$clazz.checkEmailExist") {
    async {
      customerDAO.existUserByEmail(email, UserStatus.Active)
    }
  }

  //endregion

  //region Add, Update & Delete

  override def addUser(user: Customer): Future[Customer] = Profiler(s"$clazz.addUser") {
    createCustomerLock.withLock(user.phone.orElse(user.email).getOrElse("")) {
      for {
        // must not exist active phone
        _ <- user.phone match {
          case Some(phone) => createOrUpdatePhoneLock.withLock(phone) {
            futurePoolLock {
              customerDAO.existUserByPhone(phone, UserStatus.Active) match {
                case true => throw UnsupportedException(s"Phone $phone is already exist")
                case _ =>
              }
            }
          }
          case _ => Future.Unit
        }

        // must not exist active email
        _ <- user.email match {
          case Some(email) => createOrUpdateEmailLock.withLock(email) {
            futurePoolLock {
              customerDAO.existUserByEmail(email, UserStatus.Active) match {
                case true => throw UnsupportedException(s"Email $email is already exist")
                case _ =>
              }
            }
          }
          case _ => Future.Unit
        }

        _ = customerDAO.insert(user)

        _ = logHistorical(HistoricalActionEvent.CREATED.toString, user.userId.get, None, user.toSome)

        _ = sendExtEvent("system_event_vclub_customer_created", ExtEventCode.CUSTOMER_CREATED.toString, user.toDto, JsonHelper.toNode(user.toDto))

      } yield user
    }
  }

  override def updateUser(username: String, updateReq: UpdateUserRequest): Future[Customer] = Profiler(s"$clazz.updateUser") {
    updateCustomerLock.withLock(username) {
      for {
        currUser <- internalGet(username)
        newUser = updateReq.fillWith(currUser.copy())
        _ <- internalUpdateUser(username, newUser, oldUserOpt = currUser.toSome)
      } yield newUser
    }
  }

  override def updateAvatar(username: String, avatarUrl: String): Future[Unit] = Profiler(s"$clazz.updateAvatar") {
    updateCustomerLock.withLock(username) {
      for {
        currUser <- internalGet(username)
        newUser = currUser.copy().mergeWith(Customer(avatar = avatarUrl.toSome))
        _ <- internalUpdateUser(username, newUser, oldUserOpt = currUser.toSome)
      } yield {}
    }
  }

  override def deleteUser(username: String,
                          auditor: Option[String] = None,
                          manualRequestBy: Option[String] = None,
                          manualRequestReason: Option[String] = None
                         ): Future[Unit] = Profiler(s"$clazz.deleteUser") {
    updateCustomerLock.withLock(username) {
      val currTime = System.currentTimeMillis()
      for {
        oldUser <- internalGet(username)
        _ = {
          if (!oldUser.active.contains(true)) throw UnsupportedException("Not support delete user who is not active")
        }

        oldMetadata = oldUser.metadata.getOrElse(Map.empty)
        newMetadata = {
          if (manualRequestBy.exists(_.nonEmpty)) oldMetadata ++
            Map("manual_delete_request_info" -> Map(
              "request_by" -> manualRequestBy.get,
              "reason" -> manualRequestReason,
              "deleted_by" -> auditor
            ))
          else oldMetadata
        }

        userForUpdate = Customer(
          active = UserStatus.Deactivated.toSome,
          deletedAt = currTime.toSome,
          updatedTime = currTime.toSome,
          updatedBy = auditor.orElse(Constant.USER_SELF.toSome),
          metadata = newMetadata.toSome
        )
        _ <- internalUpdateUser(
          username, user = userForUpdate, oldUserOpt = oldUser.toSome,
          removeUserFromPnlCustomers = oldUser.pnlMapping.map(_.mappings.flatMap(_._2.id).toSeq).getOrElse(Nil)
        )
        _ = identityVerificationDAO.deactivateByCustomerIdsAndActive(Seq(username.toLong), active = true)
      } yield {}
    }
  }

  private def logHistorical(action: String, userId: String, oldData: Option[Customer], newData: Option[Customer]): Future[Unit] = Profiler(s"$clazz.logHistorical") {
    val value = JsonHelper.toJson(HistoricalEvent[Customer](
      action = action,
      objectId = userId,
      oldData = oldData.map(user => if (user.password.isDefined) user.omitSensitive(isHistorical = true) else user),
      data = newData.map(c => if (c.password.isDefined) c.omitSensitive(isHistorical = true) else c),
      timestamp = System.currentTimeMillis()
    ))

    kafkaProducer.send(
      topic = customerHistoricalTopic,
      key = userId,
      value = value
    ).unit

  }

  private def sendExtEvent(topic: String, eventCode: String, customer: CustomerDTO, data: ObjectNode, pnl: Option[String] = None): Future[Unit] = Profiler(s"$clazz.sendExtEvent") {
    val fn = try {
      data.put("event_code", eventCode)
      val event = ExtEvent[ObjectNode](
        code = eventCode,
        asType = "CUSTOMER_ACCOUNT".toSome,
        originalId = customer.userId,
        customerId = customer.id,
        eventTime = System.currentTimeMillis(),
        email = customer.email,
        pnl = pnl,
        customerIdentity = CustomerIdentifyRequest(
          vclubUserId = customer.id,
          phone = customer.phone,
          email = customer.email
        ),
        data = data,
        source = "CUSTOMER_SERVICE"
      )
      kafkaProducer.send(
        topic = topic,
        key = s"${customer.userId.getOrElse("")}",
        value = JsonHelper.toJson(event)
      ).unit
    } catch {
      case e: Exception => Future.exception(e)
    }

    fn.rescue {
      case e: Exception =>
        error(s"Failed sendExtEvent($topic, $eventCode, ${JsonHelper.toJson(data)})", e)
        Future.Unit
    }

  }

  //endregion

  //region Get or Confirm Pnl Mapping

  override def getPnlMappings(userId: String, forceFetch: Boolean, reMappingPnls: Seq[String],
                              auditor: Option[String] = None,
                              manualRequestBy: Option[String] = None,
                              manualRequestReason: Option[String] = None): Future[CustomerPnlMapping] = Profiler(s"$clazz.getPnlMappings") {
    get(userId, rescanPnl = false).flatMap(c => getPnlMappings(c, forceFetch, reMappingPnls, auditor, manualRequestBy, manualRequestReason))
  }

  override def getPnlMappings(currCustomer: Customer, forceFetch: Boolean, reMappingPnls: Seq[String],
                              auditor: Option[String],
                              manualRequestBy: Option[String],
                              manualRequestReason: Option[String]): Future[CustomerPnlMapping] = Profiler(s"$clazz.getPnlMappings_Obj") {
    for {
      pnlMapping <- async {
        currCustomer.pnlMapping.map(_.vCopy())
      }

      (needMakeConsent, newConsentStatus, newConsentAt) = {
        if (pnlMapping.map(_.consentStatus).contains(CustomerPnlConsentStatus.ACCEPTED))
          (pnlMapping.forall(_.consentAt <= 0), pnlMapping.get.consentStatus, pnlMapping.map(_.consentAt).filter(_ > 0L).getOrElse(System.currentTimeMillis()))
        else
          (true, CustomerPnlConsentStatus.ACCEPTED, System.currentTimeMillis())
      }

      mappedConsented = {
        pnlMapping.map(_.mappings).getOrElse(Map.empty[String, CustomerPnlMappingData])
          //          .filter(_._2.consentStatus == CustomerPnlConsentStatus.ACCEPTED)
          .filterNot(pair => reMappingPnls.contains(pair._1))
      }

      (newPnlCustomers, hasCheck) = {
        if (forceFetch || mappedConsented.isEmpty ||
          pnlMapping.forall(_.lastCheckAt < System.currentTimeMillis() - 30.minutes.toMillis) ||
          pnlMapping.forall(_.lastCheckAt < currCustomer.updatedTime.getOrElse(0L))
        ) {
          Constant.listPnl.diff(mappedConsented.keySet.toSeq) match {
            case x if x.isEmpty => (Nil, false)
            case remainPnLs =>
              (
                pnlCustomerDAO.searchLatestActive(currCustomer.userId.get, remainPnLs,
                  currCustomer.phone, currCustomer.email,
                  currCustomer.getVerifiedIdentityNumberByType(CustomerIdentityDocType.CCCD),
                  currCustomer.getVerifiedIdentityNumberByType(CustomerIdentityDocType.PASSPORT)
                ),
                true
              )
          }
        } else (Nil, false)
      }

      mapNewMapped = newPnlCustomers.map(pnlCustomer => {
        val pnl = pnlCustomer.pnl.get
        var newCustomerPnlMappingData = CustomerPnlMappingData.apply(
          pnlCustomer,
          consentedAt = pnlMapping.flatMap(_.mappings.get(pnl)).fold(0L)(_.consentAt),
          mappedAt = pnlMapping.flatMap(_.mappings.get(pnl)).map(_.mappingAt)
        )
        val oldCustomerPnlMappingData = pnlMapping.flatMap(_.mappings.get(pnl))
        if (oldCustomerPnlMappingData.exists(_.diff(newCustomerPnlMappingData))) {
          newCustomerPnlMappingData = newCustomerPnlMappingData.copy(mappingAt = System.currentTimeMillis())
        }

        pnl -> newCustomerPnlMappingData
      }).toMap

      newPnlMappingMap = pnlMapping.map(_.mappings).fold(mapNewMapped)(mappings => {
        mappings
          .filter(mappingItem => (mappedConsented.isEmpty && reMappingPnls.isEmpty) || mappedConsented.contains(mappingItem._1)) ++
          mapNewMapped
      })
      currTime = System.currentTimeMillis()

      pnlMapping <- if (needMakeConsent || (hasCheck && pnlMapping.forall(_.mappings != newPnlMappingMap))) {

        val newPnlMapping = pnlMapping.getOrElse(CustomerPnlMapping(Map.empty, 0L)).copy(
          mappings = newPnlMappingMap,
          lastCheckAt = System.currentTimeMillis(),
          consentStatus = newConsentStatus,
          consentAt = newConsentAt
        )

        val newMetadata = currCustomer.metadata.getOrElse(Map.empty) ++ {
          if (manualRequestBy.exists(_.nonEmpty)) Map("manual_reinit_mapping_request_info" -> Map(
            "request_by" -> manualRequestBy.get,
            "reason" -> manualRequestReason
          ))
          else Map.empty
        }

        Profiler(s"$clazz.getMappingSuggestion-update") {

          internalUpdateUser(
            currCustomer.userId.get,
            Customer(
              userId = currCustomer.userId,
              updatedTime = currTime.toSome,
              pnlMapping = newPnlMapping.toSome,
              metadata = newMetadata.toSome,
              updatedBy = auditor.orElse(Constant.USER_SYSTEM.toSome)
            ),
            setUserToPnlCustomers = newPnlCustomers.flatMap(_.id),
            removeUserFromPnlCustomers = (pnlMapping.map(_.mappings.flatMap(_._2.id)).fold(Set.empty[Long])(_.toSet) -- newPnlMapping.mappings.flatMap(_._2.id).toSet).toSeq,
            oldUserOpt = currCustomer.toSome
          )
            .onSuccess(newVal => {
              val newCustomer = currCustomer.copy().mergeWith(newVal)
              notifyIfTierChangedBasedOnMigrate(force = forceFetch, currCustomer.userId.get, currCustomer, newCustomer)
              sendEventNewPnlCustomerMapped(newCustomer, newPnlMappingMap)
            })

        }.map(_ => newPnlMapping)

      } else Future.value(pnlMapping.getOrElse(CustomerPnlMapping(Map.empty, 0L)))

    } yield pnlMapping

  }

  override def confirmMappingToPnl(userId: String): Future[CustomerPnlMapping] = Profiler(s"$clazz.confirmMappingToPnl") {
    for {
      currCustomer <- get(userId, false)
      pnlMapping = currCustomer.pnlMapping.map(_.vCopy())

      (newConsentStatus, newConsentAt) = {
        if (pnlMapping.map(_.consentStatus).contains(CustomerPnlConsentStatus.ACCEPTED))
          (pnlMapping.get.consentStatus, pnlMapping.map(_.consentAt).getOrElse(System.currentTimeMillis()))
        else
          (CustomerPnlConsentStatus.ACCEPTED, System.currentTimeMillis())
      }

      mappingTable = pnlMapping.map(_.mappings).getOrElse(Map.empty[String, CustomerPnlMappingData])
      existNeedConfirm = {
        mappingTable.isEmpty ||
          pnlMapping.exists(_.consentStatus != CustomerPnlConsentStatus.ACCEPTED) ||
          mappingTable.exists(_._2.consentStatus != CustomerPnlConsentStatus.ACCEPTED)
      }

      currTime = System.currentTimeMillis()

      pnlMapping <- if (existNeedConfirm) {

        val newPnlMapping = CustomerPnlMapping(
          mappings = mappingTable.map(tuple => tuple._1 -> tuple._2.makeAccepted(currTime)),
          consentStatus = newConsentStatus,
          consentAt = newConsentAt,
          lastCheckAt = currTime
        )

        internalUpdateUser(userId, Customer(
          userId = userId.toSome,
          updatedTime = System.currentTimeMillis().toSome,
          pnlMapping = newPnlMapping.toSome
        ), oldUserOpt = currCustomer.toSome)
          .onSuccess(newVal => notifyIfTierChangedBasedOnMigrate(false, userId, currCustomer, currCustomer.copy().mergeWith(newVal)))
          .map(_ => newPnlMapping)

      } else Future.value(currCustomer.pnlMapping.getOrElse(CustomerPnlMapping(Map.empty, currTime)))

    } yield pnlMapping

  }

  private def sendEventNewPnlCustomerMapped(customer: Customer, newPnlMappingMap: Map[String, CustomerPnlMappingData]): Future[Unit] = Profiler(s"$clazz.sendEventNewPnlCustomerMapped") {
    async {
      newPnlMappingMap
    }.onSuccess(pnlMappings => {
      pnlMappings.foreach(pair => {
        val pnl = pair._1
        val mappingEvent = CustomerPnlMappedEvent(pnl)
          .fromCustomerPnlMappingData(pair._2)
          .copy(
            customerId = customer.userId.map(_.toLong),
            originalId = customer.userId,
            fullName = customer.fullName,
            lastName = customer.lastName,
            firstName = customer.lastName
          )
        sendExtEvent(s"system_event_customer_mapped_${pnl.toLowerCase}", ExtEventCode.CUSTOMER_PNL_MAPPED.toString, customer.toDto, JsonHelper.toNode(mappingEvent), pnl = pnl.toSome)
      })
    }).unit
  }

  //endregion

  //region Demographic Metadata Changed Event

  private def checkAndSendDemographicMetadataChangedEvent(oldCustomer: Customer, newCustomer: Customer): Future[Unit] = Profiler(s"$clazz.checkAndSendDemographicMetadataChangedEvent") {
    try {
      (oldCustomer.demographicMetadata, newCustomer.demographicMetadata) match {
        case (Some(oldDemographic), Some(newDemographic)) =>
          // Convert demographic metadata to JsonNode
          val oldJson = JsonHelperAllowNull.toNode[ObjectNode](oldDemographic)
          val newJson = JsonHelperAllowNull.toNode[ObjectNode](newDemographic)

          if (!newJson.equals(oldJson)) {
            // Create event data with old_ and new_ prefixes
            val eventDataMap = scala.collection.mutable.Map[String, Any]()

            // Add old data with old_ prefix
            val oldMap = JsonHelperAllowNull.fromNode[Map[String, Any]](oldJson)
            oldMap.foreach { case (key, value) =>
              eventDataMap(s"old_$key") = value
            }

            // Add new data with new_ prefix
            val newMap = JsonHelperAllowNull.fromNode[Map[String, Any]](newJson)
            newMap.foreach { case (key, value) =>
              eventDataMap(s"new_$key") = value
            }

            // Convert to ObjectNode and send the event
            val eventData = JsonHelperAllowNull.toNode[ObjectNode](eventDataMap.toMap)
            sendExtEvent(
              topic = "system_event_customer_demographic_metadata_changed",
              eventCode = ExtEventCode.CUSTOMER_DEMOGRAPHIC_METADATA_CHANGED.toString,
              customer = newCustomer.toDto,
              data = eventData
            )
          } else {
            Future.Unit
          }
        case (None, Some(newDemographic)) =>
          // Create event data with old_ and new_ prefixes
          val eventDataMap = scala.collection.mutable.Map[String, Any]()
          val newJson = JsonHelperAllowNull.toNode[ObjectNode](newDemographic)

          // Add new data with new_ prefix
          val newMap = JsonHelperAllowNull.fromNode[Map[String, Any]](newJson)
          newMap.foreach { case (key, value) =>
            eventDataMap(s"old_$key") = null
            eventDataMap(s"new_$key") = value
          }

          // Convert to ObjectNode and send the event
          val eventData = JsonHelperAllowNull.toNode[ObjectNode](eventDataMap.toMap)
          sendExtEvent(
            topic = "system_event_customer_demographic_metadata_changed",
            eventCode = ExtEventCode.CUSTOMER_DEMOGRAPHIC_METADATA_CHANGED.toString,
            customer = newCustomer.toDto,
            data = eventData
          )
        case _ => Future.Unit
      }
    } catch {
      case e: Exception =>
        error(s"Failed to check and send demographic metadata changed event", e)
        Future.Unit
    }
  }

  //endregion

  //region ReInit Mapping

  override def reInitPnlMapping(userId: String,
                                auditor: Option[String] = None,
                                manualRequestBy: Option[String] = None,
                                manualRequestReason: Option[String] = None): Future[CustomerPnlMapping] = Profiler(s"$clazz.reInitPnlMapping_ID") {
    get(userId, rescanPnl = false).flatMap(c => reInitPnlMapping(c, auditor, manualRequestBy, manualRequestReason))
  }

  override def reInitPnlMapping(customer: Customer,
                                auditor: Option[String],
                                manualRequestBy: Option[String],
                                manualRequestReason: Option[String]): Future[CustomerPnlMapping] = Profiler(s"$clazz.reInitPnlMapping") {
    customer.pnlMapping match {
      case Some(pnlMapping) if pnlMapping.mappings.map(_._2.id.get).toSeq.nonEmpty =>
        Profiler(s"$clazz.reInitRankingDay0 - item - pnlMappingNonEmpty") {
          async {
            val pnlCustomerIds = pnlMapping.mappings.map(_._2.id.get).toSeq

            val newMapPnlCustomer = pnlCustomerDAO.multiSelect(pnlCustomerIds)

            pnlMapping.mappings.filter {
              case (_, oldPnlMappingData) =>

                val newPnlCustomer = newMapPnlCustomer.find(_.id.get == oldPnlMappingData.id.get)
                val newPnlMappingData = newPnlCustomer.map(CustomerPnlMappingData.apply(_, oldPnlMappingData.consentAt, None))

                newPnlCustomer.isEmpty ||
                  newPnlCustomer.get.active.contains(false) ||
                  newPnlMappingData.get.diff(oldPnlMappingData) ||
                  !oldPnlMappingData.isSameIdentify(customer.phone, customer.email, customer.getVerifiedIdentityNumberByType(CustomerIdentityDocType.CCCD), customer.getVerifiedIdentityNumberByType(CustomerIdentityDocType.PASSPORT))
            }
          }.flatMap(needReMapping => {
            if (needReMapping.nonEmpty)
              Profiler(s"$clazz.reInitRankingDay0 - item - needReMappingNonEmpty - getNewMapping") {
                getPnlMappings(customer.userId.get, true, needReMapping.keys.toSeq, auditor, manualRequestBy, manualRequestReason)
              }
            else
              Profiler(s"$clazz.reInitRankingDay0 - item - needReMappingIsEmpty - getNewMapping") {
                getPnlMappings(customer.userId.get, true, Nil, auditor, manualRequestBy, manualRequestReason)
              }
          })
        }
      case _ =>
        Profiler(s"$clazz.reInitRankingDay0 - item - pnlMappingIsEmpty - getNewMapping") {
          getPnlMappings(customer.userId.get, true, Nil, auditor, manualRequestBy, manualRequestReason)
        }
    }
  }

  //endregion

  //region Check ranking progress and notify tier change

  private def notifyIfTierChangedBasedOnMigrate(force: Boolean, userId: String, oldCustomer: Customer, newCustomer: Customer): Future[Unit] = Profiler(s"$clazz.notifyIfTierChangedBasedOnMigrate") {
    for {
      tiersOrdered <- internalService.getTiersCodeOrdered()
      tiersCodeOrdered = tiersOrdered.map(_.code)

      oldInitTierCode: String = oldCustomer.pnlMapping.map(_.mappings.values.toSeq).getOrElse(Nil)
        .flatMap(_.initTierCode)
        .filter(tiersCodeOrdered.contains)
        .sortWith((v1, v2) => tiersCodeOrdered.indexOf(v1) > tiersCodeOrdered.indexOf(v2))
        .headOption.getOrElse("MEMBER")

      newInitTierCode: String = newCustomer.pnlMapping.map(_.mappings.values.toSeq).getOrElse(Nil)
        .flatMap(_.initTierCode)
        .filter(tiersCodeOrdered.contains)
        .sortWith((v1, v2) => tiersCodeOrdered.indexOf(v1) > tiersCodeOrdered.indexOf(v2))
        .headOption.getOrElse("MEMBER")

      _ = {

        val consentAccepted = newCustomer.pnlMapping.map(_.consentStatus).contains(CustomerPnlConsentStatus.ACCEPTED)
        val initTierChange = oldInitTierCode != newInitTierCode && newCustomer.pnlMapping.map(_.consentStatus).contains(CustomerPnlConsentStatus.ACCEPTED)

        val isChangeToAccepted = oldCustomer.pnlMapping.map(_.consentStatus) != newCustomer.pnlMapping.map(_.consentStatus) &&
          newCustomer.pnlMapping.exists(_.consentStatus == CustomerPnlConsentStatus.ACCEPTED)

        val currTierCode = newCustomer.tierIds.flatMap(_.lastOption).flatMap(tierId => tiersOrdered.find(_.id == tierId).map(_.code))
        val isUpgradeFromCurrentTier = currTierCode.isEmpty || tiersCodeOrdered.indexOf(currTierCode.get) < tiersCodeOrdered.indexOf(newInitTierCode)

        val isBefore31Dec2025 = System.currentTimeMillis() < 1767243600000L

        if ((force || initTierChange || isChangeToAccepted) && consentAccepted && isUpgradeFromCurrentTier && isBefore31Dec2025) {
          Profiler(s"$clazz.notifyIfTierChanged-kafkaSend") {
            kafkaProducer.send(
              topic = customerTierChangedTopic,
              key = userId,
              value = JsonHelper.toJson(CustomerTierChanged(
                source = "pnl_mapping_migrate_tier_code",
                timestamp = newCustomer.updatedTime.getOrElse(System.currentTimeMillis()),
                customerId = userId,
                oldTierId = None, oldTierCode = oldInitTierCode.toSome,
                newTierId = None, newTierCode = newInitTierCode.toSome,
                pnlMapping = newCustomer.pnlMapping,
                dataTime = newCustomer.pnlMapping.map(_.lastCheckAt)
              ))
            ).unit
          }
        }
      }

    } yield {}
  }

  override def updateRankingProgress(username: String, pnlRankingProgress: CustomerPnlRankingProgress, oldUserOpt: Option[Customer]): Future[Unit] = Profiler(s"$clazz.updateRankingProgress") {
    updateCustomerLock.withLock(username) {
      for {
        currUser <- oldUserOpt.fold(internalGet(username))(Future.value)

        tiers <- internalService.getTiersCodeOrdered()

        isChangePnlRankingProgress = checkIsChangePnlRankingProgress(currUser.pnlRankingProgress, pnlRankingProgress)

        (isChangePnlTiersDemographic, demographicForUpdate) = checkIsChangePnlTiersDemographic(currUser, pnlRankingProgress)

        _ <- Profiler(s"$clazz.updateRankingProgress - isChangeProgress $isChangePnlRankingProgress - isChangeTiersDemographic $isChangePnlTiersDemographic") {
          if (isChangePnlRankingProgress || isChangePnlTiersDemographic) {
            internalUpdateUser(username, Customer(pnlRankingProgress = pnlRankingProgress.toSome, demographicMetadata = demographicForUpdate.toSome), oldUserOpt = currUser.toSome)
              .onSuccess(newCustomer => {

                val currTierCode = currUser.tierIds.getOrElse(Nil).lastOption
                  .flatMap(tierId => tiers.find(_.id == tierId).map(_.code))

                val newTierCode = pnlRankingProgress.tierCode.filter(_.nonEmpty)
                val newTierId = tiers.find(v => newTierCode.contains(v.code)).map(_.id)

                if (currTierCode != newTierCode && newTierCode.nonEmpty) {
                  Profiler(s"$clazz.notifyIfTierChanged-kafkaSend") {
                    kafkaProducer.send(
                      topic = customerTierChangedTopic,
                      key = username,
                      value = JsonHelper.toJson(CustomerTierChanged(
                        source = "calc_tier_code",
                        timestamp = newCustomer.updatedTime.getOrElse(System.currentTimeMillis()),
                        customerId = username,
                        oldTierId = None, oldTierCode = None,
                        newTierId = newTierId, newTierCode = newTierCode,
                        pnlMapping = newCustomer.pnlMapping,
                        dataTime = pnlRankingProgress.dataTime
                      ))
                    ).unit
                  }
                }
              })
          } else Future.Unit
        }
      } yield {}
    }.onFailure(ex => {
      error(s"Failed when updateRankingProgress($username, ${JsonHelper.toJson(pnlRankingProgress)}, ${JsonHelper.toJson(oldUserOpt)})", ex)
    })
  }

  private def checkIsChangePnlRankingProgress(currentProgress: Option[CustomerPnlRankingProgress], newProgress: CustomerPnlRankingProgress): Boolean = {
    currentProgress.isEmpty ||
      newProgress.dataTime.exists(_ > currentProgress.get.dataTime.getOrElse(0L)) ||
      currentProgress.get.tierCode != newProgress.tierCode || {
      currentProgress.get.hasChange(newProgress)
    }
  }

  //endregion

  override def searchActiveUserId(phone: Seq[String], email: Seq[String], identityNumbers: Seq[String]): Future[Seq[String]] = Profiler(s"$clazz.searchActive") {
    async {
      customerDAO.searchActiveUserId(phones = phone, emails = email, identityNumbers = identityNumbers)
    }
  }

  //region update customer identity document
  override def verifyIdentity(
                               customerId: String,
                               identityDoc: CustomerIdentityDocument,
                               identityFilesReq: Seq[BytesFile], faceImageFileReq: Option[BytesFile],
                               stageMetadata: Option[String], customAddress: Option[String]
                             ): Future[CustomerIdentityDocument] = Profiler(s"$clazz.verifyIdentity") {

    updateCustomerLock.withLock(customerId) {
      val fn = for {
        customer <- get(customerId, rescanPnl = false)
        _ = _validateWithCustomerIdentity(customer, identityDoc.`type`)

        // Resize image trước khi gọi qua OCR
        // FileID -> URL
        resizedIdentityMapUrl = identityFilesReq.flatMap(file => {
          if (file.isImage) {
            (file.id, imgProxyService.resizeFromFile(new File(file.localPath), width = None, height = 1024, extension = None, contentType = file.contentType, mac = customerId)).toSome
          } else None
        }).toMap

        // Build ra list presign url để tiến hành đi OCR trước,
        // nếu OCR ko thành công sẽ báo lỗi và ko upload bất kì file nào
        identifyInfo <- processOCR(
          identityDoc.`type`.get,
          identityFilesReq.flatMap(file => resizedIdentityMapUrl.get(file.id)),
          customAddress = customAddress
        )

        _ = _validateWithDocumentVerification(identityDoc, identifyInfo)

        // Upload file and update customer profile
        result <- if (identifyInfo.isComplete && faceImageFileReq.isDefined) {

          for {
            (faceImageFile, resizedFaceImageUrl) <- async {
              val faceImageFile = faceImageFileReq.get
              val resizedFaceImageUrl = imgProxyService.resizeFromFile(new File(faceImageFile.localPath), width = None, height = 1024, extension = None, contentType = faceImageFile.contentType, mac = customerId)
              CustomUtils.downloadPhoto(resizedFaceImageUrl, faceImageFile.localPath)
              (
                faceImageFile.copy(filePath = s"${System.currentTimeMillis()}_${faceImageFile.filePath}"),
                resizedFaceImageUrl
              )
            }

            identityFiles = identityFilesReq.map(file => {
              resizedIdentityMapUrl.get(file.id) match {
                case Some(fileUrl) =>
                  CustomUtils.downloadPhoto(fileUrl, file.localPath)
                  file.copy(filePath = s"${System.currentTimeMillis()}_${file.filePath}")
                case _ =>
                  file
              }
            })

            faceMatchingResult <- aiIntegrationService.faceMatching(
              identityFilesReq.flatMap(i => resizedIdentityMapUrl.get(i.id)),
              resizedFaceImageUrl
            )
            scores = CustomerIdentityVerificationScores(
              faceMatching = faceMatchingResult.map(_._2),
              faceMatchingLabel = faceMatchingResult.map(_._1)
            )

            identityFiles <- customerFileService.uploadFilesCustomerPhotoCard(customerId.toLong, identityFiles)
            faceFiles <- customerFileService.uploadFilesCustomerFace(customerId.toLong, Seq(faceImageFile))

            (newCustomer, customerIdentityDocument) <- {
              val (newCustomer, customerIdentityDocument) = customerDAO.executeTransaction(customerDAO.ds)(conn => {

                val currTime = System.currentTimeMillis()

                val customerIdentityDocument = CustomerIdentityVerification(
                  id = None,
                  customerId = customerId.toLong.toSome,
                  identityType = identityDoc.`type`,
                  no = identifyInfo.no.toSome,
                  data = identifyInfo.toSome,
                  identityImages = identityFiles.toSome,
                  faceImages = faceFiles.toSome,
                  createdAt = currTime.toSome,
                  updatedAt = currTime.toSome,
                  scores = scores.toSome
                )
                _saveDocumentIdentityInfo(customerId, identityDoc, customer, identifyInfo, conn, currTime, customerIdentityDocument)
              })

              logHistorical(HistoricalActionEvent.UPDATED.toString, customerId, customer.toSome, customer.copy().mergeWith(newCustomer).toSome)
                .map(_ => (newCustomer, customerIdentityDocument))

            }

          } yield (newCustomer, customerIdentityDocument).toSome

        } else Future.None

      } yield {

        if (identifyInfo.isComplete && faceImageFileReq.isDefined) identityDoc.nextStage = "completed".toSome
        else if (identifyInfo.isComplete && faceImageFileReq.isEmpty) identityDoc.nextStage = "need_face_image".toSome
        else if (!identifyInfo.isComplete) identityDoc.nextStage = s"need_identity_document_${identityFilesReq.size + 1}".toSome

        identityDoc.stageMetadata = stageMetadata.orElse(UUID.randomUUID().toString.toSome)
        identityDoc.idInfo = identifyInfo.toSome
        identityDoc.idScores = result.flatMap(_._2.scores)
        identityDoc
      }
      fn.onFailure(e => error(s"Failed verifyIdentity($customerId) ${e.getMessage}"))
    }
  }

  override def verifyIdentityByProvider(customerId: String, documentInfo: EKYCDocumentVerification, sendAutoApproval: Boolean = false): Future[CustomerIdentityDocument] = Profiler(s"$clazz.verifyIdentityByProvider_${documentInfo.provider}") {
    val identityDoc = documentInfo.identityDoc
    updateCustomerLock.withLock(customerId) {
      val fn = for {
        customer <- get(customerId, rescanPnl = false)
        _ = _validateWithCustomerIdentity(customer, identityDoc.`type`)

        identifyInfo = documentInfo.identityInfo

        _ = _validateOcrResult(identityDoc.`type`.get, documentInfo.identityFiles.getOrElse(Seq.empty), identifyInfo)
        _ = _validateWithDocumentVerification(identityDoc, identifyInfo)

        // Upload file and update customer profile
        result <- if (identifyInfo.isComplete && documentInfo.faceImageFiles.isDefined) {
          for {
            identityFiles <- customerFileService.uploadFilesCustomerPhotoCard(customerId.toLong, documentInfo.identityFiles.get)
            identityRawFiles <- {
              documentInfo.identityRawFiles match {
                case Some(rawFiles) => customerFileService.uploadFilesRawCustomerPhotoCard(customerId.toLong, rawFiles)
                case _ => Future.value(Seq.empty)
              }
            }
            faceFiles <- customerFileService.uploadFilesCustomerFace(customerId.toLong, documentInfo.faceImageFiles.get)

            chipFiles <- {
              documentInfo.chipImageFiles match {
                case Some(chipImageFiles) => customerFileService.uploadFilesChipCustomerFace(customerId.toLong, chipImageFiles)
                case _ => Future.value(Seq.empty)
              }
            }

            (newCustomer, customerIdentityDocument) <- {
              val (newCustomer, customerIdentityDocument) = customerDAO.executeTransaction(customerDAO.ds)(conn => {

                val currTime = System.currentTimeMillis()
                val chipData = documentInfo.chipData.map(data => data.copy(chipImage = chipFiles.toSome))

                val customerIdentityDocument = CustomerIdentityVerification(
                  id = None,
                  customerId = customerId.toLong.toSome,
                  identityType = identityDoc.`type`,
                  no = identifyInfo.no.toSome,
                  data = identifyInfo.toSome,
                  identityImages = (identityFiles ++ identityRawFiles).toSome,
                  faceImages = faceFiles.toSome,
                  createdAt = currTime.toSome,
                  updatedAt = currTime.toSome,
                  scores = documentInfo.faceMatchingScore.toSome,
                  metadata = documentInfo.metadata,
                  chipData = chipData,
                  provider = documentInfo.provider.toSome
                )
                _saveDocumentIdentityInfo(customerId, identityDoc, customer, identifyInfo, conn, currTime, customerIdentityDocument)
              })

              logHistorical(HistoricalActionEvent.UPDATED.toString, customerId, customer.toSome, customer.copy().mergeWith(newCustomer).toSome)
                .map(_ => (newCustomer, customerIdentityDocument))
            }
          } yield (newCustomer, customerIdentityDocument).toSome

        } else Future.None

      } yield {
        identityDoc.nextStage = documentInfo.nextStep match {
          case Some(step) => step match {
            case EKYCVerifyStep.SCAN_FRONT_DOCUMENT => "need_identity_document_1".toSome
            case EKYCVerifyStep.SCAN_BACK_DOCUMENT => "need_identity_document_2".toSome
            case EKYCVerifyStep.SCAN_NFC => "need_chip_data".toSome
            case EKYCVerifyStep.SCAN_FACE => "need_face_image".toSome
          }
          case _ =>
            if (sendAutoApproval) sendAutoApproveIdentityVerificationEvent(customerId, result.map(_._2).get)
            "completed".toSome
        }
        identityDoc.stageMetadata = identityDoc.stageMetadata.orElse(UUID.randomUUID().toString.toSome)
        identityDoc.idInfo = identifyInfo.toSome
        identityDoc.idScores = result.flatMap(_._2.scores)
        identityDoc
      }
      fn.onFailure(e => error(s"Failed verifyIdentityByProvider_${documentInfo.provider}($customerId) ${e.getMessage}"))
    }
  }

  private def _validateWithCustomerIdentity(customer: Customer, identityType: Option[String]): Unit = {
    customer.listIdentityDocument.getOrElse(Nil).find(i => i.`type`.isDefined && i.`type` == identityType) match {
      case None =>
      case Some(v) if v.verifiedStatus.contains(CustomerIdentityVerifyStatus.REJECTED.toString) =>
      case Some(v) if v.verifiedStatus.contains(CustomerIdentityVerifyStatus.SUBMITTED.toString) =>
        throw RCustomException("identity_document_already_submitted", "Identity document is already submitted")
      case _ =>
        throw RCustomException("identity_document_already_verified", "Identity document is already verified")
    }
  }

  private def _validateWithDocumentVerification(identityDoc: CustomerIdentityDocument, identifyInfo: CustomerIdentityDocumentInfo): Unit = {
    if (
      identityVerificationDAO.existTypeAndNoAndApprovalStatus(
        identityDoc.`type`.get, identifyInfo.no, Seq("WAITING", "PROCESSING", "SUCCESS"), None
      )
    ) {
      throw RCustomException("already_exist_customer_identity", s"Customer identity already exist")
    }
  }

  private def _saveDocumentIdentityInfo(customerId: String, identityDoc: CustomerIdentityDocument, customer: Customer, identifyInfo: CustomerIdentityDocumentInfo, conn: Connection, currTime: Long, customerIdentityDocument: CustomerIdentityVerification) = {
    val cidID = identityVerificationDAO.insertAndGetId(customerIdentityDocument, connection = Some(conn))
      .getOrElse(throw RCustomException("cannot_save_identity_document", "Cannot save identity document"))
    customerIdentityDocument.id = cidID.toSome

    val newListIdentityDocument = customer.listIdentityDocument.getOrElse(Nil).filterNot(_.`type` == identityDoc.`type`)

    val newCustomer = Customer()
    newCustomer.updatedTime = currTime.toSome
    newCustomer.userId = customerId.toSome
    newCustomer.listIdentityDocument = (newListIdentityDocument :+ CustomerIdentityDocument(
      `type` = identityDoc.`type`,
      no = identifyInfo.no.toSome,
      verifiedStatus = CustomerIdentityVerifyStatus.SUBMITTED.toString.toSome,
      verifiedStatusMessage = "".toSome,
      identityDocumentVerifyId = cidID.toSome
    )).toSome

    customerDAO.update(newCustomer, connection = Some(conn))
    (customer.copy().mergeWith(newCustomer), customerIdentityDocument)
  }

  override def updateIdentifyVerifyStatus(customerId: String,
                                          verifyStatus: CustomerIdentityVerifyStatus, verifyStatusMessage: String,
                                          identityType: String,
                                          identityDocumentVerifyId: Long): Future[Unit] = Profiler(s"$clazz.updateIdentifyVerifyStatus") {
    updateCustomerLock.withLock(customerId) {
      for {
        customer <- get(customerId, rescanPnl = false)

        (listIdentityDocumentForUpdate, itemUpdate) = {

          var itemUpdate: Option[CustomerIdentityDocument] = None

          val newList = customer.listIdentityDocument.getOrElse(Nil).map(v =>
            //                 && !v.verifiedStatus.contains(verifyStatus)
            if (v.`type`.contains(identityType) && v.identityDocumentVerifyId.contains(identityDocumentVerifyId)) {
              val r = v.copy(verifiedStatus = verifyStatus.toString.toSome, verifiedStatusMessage = verifyStatusMessage.toSome)
              itemUpdate = Some(r)
              r
            } else v
          )

          if (itemUpdate.isEmpty) throw RCustomException("identity_document_not_found", "Identity document not found")

          (newList, itemUpdate.get)
        }

        _ = {
          if (
            !CustomerIdentityVerifyStatus.isRejected(verifyStatus) &&
              identityVerificationDAO.existTypeAndNoAndApprovalStatus(
                itemUpdate.`type`.get, itemUpdate.no.get, Seq("WAITING", "PROCESSING", "SUCCESS"), excludeCustomerId = Some(customerId.toLong)
              )) {
            throw RCustomException("already_exist_customer_identity", s"Customer identity already exist on other customer")
          }
        }

        _ <- internalUpdateUser(
          customerId,
          Customer(userId = customerId.toSome, listIdentityDocument = listIdentityDocumentForUpdate.toSome)
        ).onSuccess(c => getPnlMappings(c.userId.get, forceFetch = true, Nil)) // rescan pnl mapping when customer identity VERIFIED or REJECTED

      } yield {}
    }
  }

  private def sendAutoApproveIdentityVerificationEvent(customerId: String, identityVerification: CustomerIdentityVerification): Future[Unit] = Profiler(s"$clazz.sendAutoApproveIdentityVerificationEvent") {
    val value = JsonHelper.toJson(
      Map(
        "customerId" -> customerId,
        "identityDocumentVerifyId" -> identityVerification.id.get,
        "identityType" -> identityVerification.identityType.get,
        "identityNo" -> identityVerification.no.get,
        "provider" -> identityVerification.provider.get,
      )
    )

    kafkaProducer.send(
      topic = autoApproveIdentityVerificationTopic,
      key = customerId,
      value = value
    ).unit
  }

  private def processOCR(identityType: String, identifyFileUrls: Seq[String],
                         customAddress: Option[String]): Future[CustomerIdentityDocumentInfo] = Profiler(s"$clazz.processOCR") {

    aiIntegrationService.extractIdentify(CustomerIdentityDocType.get(identityType), identifyFileUrls).map(resp => {

      val result = resp.getOrElse(throw RCustomException("cannot_parse_identity_document_data", "Invalid customer identify file data"))

      if (customAddress.isDefined) {
        result.ocrAddress = result.address
        result.address = customAddress
      }

      _validateOcrResult(identityType, identifyFileUrls, result)

      result

    })
  }

  private def _validateOcrResult(identityType: String, identifyFiles: Seq[Any], result: CustomerIdentityDocumentInfo): Unit = {
    CustomerIdentityDocType.get(identityType) match {
      case CustomerIdentityDocType.CCCD =>

        var fieldsEmpty = Seq(
          ("full_name", result.fullName),
          ("gender", result.gender),
          ("dob", result.dob),
          ("nationality", result.nationality),
          ("hometown", result.hometown),
          ("address", result.address),
          ("expired_date", result.expiredDate)
        ).filter(_._2.isEmpty).map(_._1)

        if (identifyFiles.nonEmpty && fieldsEmpty.nonEmpty)
          throw RCustomException("cannot_parse_cccd_identity_document_1", s"Invalid customer cccd identify file 1, missing ${fieldsEmpty.mkString(", ")}")

        fieldsEmpty = Seq(
          ("issued_by", result.issuedBy),
          ("issued_date", result.issuedDate)
        ).filter(_._2.isEmpty).map(_._1)

        if (identifyFiles.size == 2 && fieldsEmpty.nonEmpty)
          throw RCustomException("cannot_parse_cccd_identity_document_2", s"Invalid customer cccd identify file 2, missing ${fieldsEmpty.mkString(", ")}")

      case CustomerIdentityDocType.PASSPORT =>
        val fieldsEmpty = Seq(
          ("full_name", result.fullName),
          ("gender", result.gender),
          ("dob", result.dob),
          ("nationality", result.nationality),
          ("issued_by", result.issuedBy),
          ("issued_date", result.issuedDate),
          ("expired_date", result.expiredDate)
        ).filter(_._2.isEmpty).map(_._1)

        if (fieldsEmpty.nonEmpty)
          throw RCustomException("cannot_parse_passport_identity_document_1", s"Invalid customer identify passport file data, missing ${fieldsEmpty.mkString(", ")}")

      case x => throw UnsupportedException(s"unsupported identity type ${x.toString}")

    }
  }

  override def canVerifyIdentityDocument(customerId: String): Future[Unit] = Profiler(s"$clazz.checkCustomerIdentityVerified") {
    for {
      customer <- get(customerId, rescanPnl = false)
      _ = {
        customer.listIdentityDocument.getOrElse(Nil).find(i => i.verifiedStatus.isDefined) match {
          case None =>
          case Some(v) if v.verifiedStatus.contains(CustomerIdentityVerifyStatus.REJECTED.toString) =>
          case Some(v) if v.verifiedStatus.contains(CustomerIdentityVerifyStatus.SUBMITTED.toString) =>
            throw RCustomException("identity_document_already_submitted", "Identity document is already submitted")
          case _ =>
            throw RCustomException("identity_document_already_verified", "Identity document is already verified")
        }
      }
    } yield {}
  }

  // endregion

  // region demographic_metadata
  override def extractDemographicDataByVerifiedIdentity(customerIdentityVerifiedEvent: CustomerIdentityVerifiedEvent): Future[Unit] = Profiler(s"$clazz.extractDemographicDataByVerifiedIdentity") {
    val customerId = customerIdentityVerifiedEvent.customerId.toString
    updateCustomerLock.withLock(customerId) {
      for {
        customer <- get(customerId, rescanPnl = false)

        matchingDoc = customer.listIdentityDocument.getOrElse(Nil).find(_.identityDocumentVerifyId.contains(customerIdentityVerifiedEvent.identityDocumentVerifyId))
          .getOrElse(throw RCustomException("invalid_customer_identity_verified_event", "Identity document not found"))

        _ = {
          if (!matchingDoc.`type`.contains(customerIdentityVerifiedEvent.identityType) ||
            matchingDoc.verifiedStatus.contains(CustomerIdentityVerifyStatus.REJECTED.toString)) {
            throw RCustomException("invalid_customer_identity_verified_event", "Invalid identity document")
          }
        }

        data = identityVerificationDAO.select(customerIdentityVerifiedEvent.identityDocumentVerifyId).map { verifyDoc =>
          if (!verifyDoc.customerId.contains(customerIdentityVerifiedEvent.customerId) ||
            !verifyDoc.identityType.contains(customerIdentityVerifiedEvent.identityType) ||
            !verifyDoc.no.contains(customerIdentityVerifiedEvent.identityNo) ||
            !verifyDoc.approvalStatus.contains("SUCCESS") ||
            !verifyDoc.approvedBy.contains(customerIdentityVerifiedEvent.approvedBy)) {
            throw RCustomException("invalid_customer_identity_verified_event", "Invalid identity document data")
          }
          verifyDoc.data.getOrElse(throw RCustomException("invalid_customer_identity_verified_event", "Identity document data not found"))
        }.getOrElse(throw RCustomException("invalid_customer_identity_verified_event", "Identity document not found"))

        demographicMetadataForUpdate = {
          var demographicMetadata = customer.demographicMetadata.getOrElse(DemographicMetadata())
          val baseMetadata = demographicMetadata.copy(
            birthday = CTimeUtils.formatToIsoDate(data.dob),
            yearOfBirth = CTimeUtils.extractDatePart(data.dob, DatePart.YEAR),
            monthOfBirth = CTimeUtils.extractDatePart(data.dob, DatePart.MONTH),
            fullName = data.fullName,
            gender = CustomUtils.getGenderCode(data.gender),
            nationality = data.nationality,
            permanentAddress = data.address,
            hometown = data.hometown
          )

          val addressesToExtract = data.address.toSeq ++ data.hometown.toSeq
          aiIntegrationService.extractAddress(addressesToExtract) match {
            case Some(detailedAddresses) =>
              val permanentDetailsOpt = if (data.address.isDefined) detailedAddresses.headOption else None
              val hometownDetailsOpt = if (data.hometown.isDefined) detailedAddresses.lift(if (data.address.isDefined) 1 else 0) else None

              demographicMetadata = baseMetadata.copy(
                permanentAddressRegionCode = AddressHelper.getRegionCodeByProvinceName(permanentDetailsOpt.flatMap(_.province)),
                permanentAddressProvinceCode = AddressHelper.getProvinceCode(permanentDetailsOpt.flatMap(_.province)),
                hometownRegionCode = AddressHelper.getRegionCodeByProvinceName(hometownDetailsOpt.flatMap(_.province)),
                hometownProvinceCode = AddressHelper.getProvinceCode(hometownDetailsOpt.flatMap(_.province)),
              )
            case _ =>
              demographicMetadata = baseMetadata
          }
          demographicMetadata
        }

        _ <- internalUpdateUser(
          customerId,
          Customer(userId = customerId.toSome, demographicMetadata = demographicMetadataForUpdate.toSome),
        )

      } yield {}
    }
  }

  override def removeDemographicDataByRejectedIdentity(customerIdentityRejectedEvent: CustomerIdentityRejectedEvent): Future[Unit] = Profiler(s"$clazz.removeDemographicDataByRejectedIdentity") {
    val customerId = customerIdentityRejectedEvent.customerId.toString
    updateCustomerLock.withLock(customerId) {
      for {
        customer <- get(customerId, rescanPnl = false)

        _ = {
          if (customer.demographicMetadata.isEmpty) {
            throw VhmCustomException("invalid_customer_identity_reject_event", "No demographic data to remove")
          }
        }

        matchingDoc = customer.listIdentityDocument.getOrElse(Nil).find(_.identityDocumentVerifyId.contains(customerIdentityRejectedEvent.identityDocumentVerifyId))
          .getOrElse(throw RCustomException("invalid_customer_identity_reject_event", "Identity document not found"))

        _ = {
          if (!matchingDoc.`type`.contains(customerIdentityRejectedEvent.identityType) || // check identity type
            (matchingDoc.verifiedStatus.contains(CustomerIdentityVerifyStatus.REJECTED.toString) && customer.demographicMetadata.exists(_.isAllIdentityDataEmpty))) {
            throw RCustomException("invalid_customer_identity_reject_event", "Invalid identity document")
          }
        }

        _ = identityVerificationDAO.select(customerIdentityRejectedEvent.identityDocumentVerifyId).foreach { verifyDoc =>
          if (!verifyDoc.customerId.contains(customerIdentityRejectedEvent.customerId) ||
            !verifyDoc.identityType.contains(customerIdentityRejectedEvent.identityType) ||
            !verifyDoc.no.contains(customerIdentityRejectedEvent.identityNo) ||
            !verifyDoc.approvalStatus.contains("FAILED") ||
            !verifyDoc.approvedBy.contains(customerIdentityRejectedEvent.rejectedBy)) {
            throw RCustomException("invalid_customer_identity_reject_event", "Invalid identity document data")
          }
        }

        demographicMetadataForUpdate = customer.demographicMetadata.getOrElse(DemographicMetadata()).copy(
          birthday = None,
          yearOfBirth = None,
          monthOfBirth = None,
          fullName = None,
          gender = None,
          permanentAddress = None,
          permanentAddressRegionCode = None,
          permanentAddressProvinceCode = None,
          hometown = None,
          hometownRegionCode = None,
          hometownProvinceCode = None,
          nationality = None
        )

        _ <- internalUpdateUser(
          customerId,
          Customer(userId = customerId.toSome, demographicMetadata = demographicMetadataForUpdate.toSome),
        )

      } yield {}
    }
  }

  override def updateCdpProfileDemographic(username: String, customerCdpProfile: CustomerCdpProfile, oldUserOpt: Option[Customer]): Future[Unit] = Profiler(s"$clazz.updateCdpProfileDemographic") {
    updateCustomerLock.withLock(username) {
      for {
        currUser <- oldUserOpt.fold(internalGet(username))(Future.value)

        (isChangeCdpProfileDemographic, demographicForUpdate) = checkIsChangeCdpProfileDemographic(currUser, customerCdpProfile)
        metadataExtraForUpdate = currUser.demographicMetadataExtra.getOrElse(Map.empty) ++ customerCdpProfile.livingAddressDisplayNameMetadata

        _ <- Profiler(s"$clazz.updateCdpProfileDemographic - isChangeCdpProfileDemoGraphic $isChangeCdpProfileDemographic") {
          if (isChangeCdpProfileDemographic) {
            // TODO: SangLNN change update demographic metadata and metadata using incremental update using jsonb merge sql
            internalUpdateUser(username, Customer(demographicMetadata = demographicForUpdate.toSome, demographicMetadataExtra = metadataExtraForUpdate.toSome), oldUserOpt = currUser.toSome).unit
          } else if (currUser.demographicMetadataExtra.isEmpty && metadataExtraForUpdate.nonEmpty) {
            Profiler(s"$clazz.updateCdpProfileDemographic - forceUpdateMetadata") {
              internalUpdateUser(username, Customer(demographicMetadataExtra = metadataExtraForUpdate.toSome), oldUserOpt = currUser.toSome).unit
            }
          } else Future.Unit
        }
      } yield {}
    }.onFailure(ex => {
      error(s"Failed when updateCdpProfileDemographic($username, ${JsonHelper.toJson(customerCdpProfile)}, ${JsonHelper.toJson(oldUserOpt)})", ex)
    })
  }

  private def checkIsChangePnlTiersDemographic(currUser: Customer, pnlRankingProgress: CustomerPnlRankingProgress): (Boolean, DemographicMetadata) = {
    val mappingPnls = currUser.pnlMapping.map(_.mappings.keys).toSeq.flatten
    val demographicMetadata = currUser.demographicMetadata.getOrElse(DemographicMetadata())
    val oldPnlTiers = demographicMetadata.getAllPnlTiers
    val newPnlTiers = pnlRankingProgress.pnlProgress.collect {
      case (pnl, progress) if progress.tierCode.isDefined && mappingPnls.contains(pnl) => pnl -> progress.tierCode.get
    }

    val hasChange = oldPnlTiers != newPnlTiers

    if (hasChange) {
      (true, demographicMetadata.setAllPnlTiers(newPnlTiers))
    } else {
      (false, demographicMetadata)
    }
  }

  private def checkIsChangeCdpProfileDemographic(currUser: Customer, customerCdpProfile: CustomerCdpProfile): (Boolean, DemographicMetadata) = {
    val demographicMetadata = currUser.demographicMetadata.getOrElse(DemographicMetadata())
    // check if appInteractionTime has changed
    val oldFirstAppInteractionTime = demographicMetadata.firstAppInteractionTime
    val newFirstAppInteractionTime = customerCdpProfile.firstAppInteractionTime
    val oldLastAppInteractionTime = demographicMetadata.lastAppInteractionTime
    val newLastAppInteractionTime = customerCdpProfile.lastAppInteractionTime
    val hasChangeAppInteractionTime = (oldFirstAppInteractionTime != newFirstAppInteractionTime) || (oldLastAppInteractionTime != newLastAppInteractionTime)

    // check if pnlSpendingAmount has changed
    val oldPnlSpendingAmount = demographicMetadata.getAllPnlSpendingAmount
    val newPnlSpendingAmount = customerCdpProfile.pnlSpendingAmount.collect {
      case (pnl, amount) if amount > 0 => pnl -> amount
    }
    val hasChangePnlSpendingAmount = oldPnlSpendingAmount != newPnlSpendingAmount

    // check if livingAddress has changed
    val oldLivingAddressProvinceCode = demographicMetadata.livingAddressProvinceCode
    val oldLivingAddressRegionCode = demographicMetadata.livingAddressRegionCode
    val newLivingAddressProvinceCode = customerCdpProfile.livingAddress.provinceId.map(id => id.toString)
    val newLivingAddressRegionCode = customerCdpProfile.livingAddress.regionId.map(id => id.toString)
    val hasChangeLivingAddress = (oldLivingAddressProvinceCode != newLivingAddressProvinceCode) || (oldLivingAddressRegionCode != newLivingAddressRegionCode)

    // check if customer scores have changed
    val oldCustomerScores = demographicMetadata.getAllCustomerScores
    val oldCustomerScoreRanks = demographicMetadata.getAllCustomerScoreRanks
    val newCustomerScores = customerCdpProfile.scores.map { case (k, v) => k -> v.score.getOrElse("") }
    val newCustomerScoreRanks = customerCdpProfile.scores.map { case (k, v) => k -> v.rank.getOrElse("") }
    val hasChangeCustomerScores = (oldCustomerScores != newCustomerScores) || (oldCustomerScoreRanks != newCustomerScoreRanks)

    if (hasChangePnlSpendingAmount || hasChangeLivingAddress || hasChangeAppInteractionTime || hasChangeCustomerScores) {
      (true, demographicMetadata.setAllPnlSpendingAmount(newPnlSpendingAmount).setAllCustomerScores(newCustomerScores).setAllCustomerScoreRanks(newCustomerScoreRanks)
        .copy(
          livingAddressRegionCode = newLivingAddressRegionCode, livingAddressProvinceCode = newLivingAddressProvinceCode,
          firstAppInteractionTime = newFirstAppInteractionTime, lastAppInteractionTime = newLastAppInteractionTime,
          firstAppInteractionDate = CTimeUtils.epochToTimestampTz(newFirstAppInteractionTime, throwExceptionWhenFail = false),
          lastAppInteractionDate = CTimeUtils.epochToTimestampTz(newLastAppInteractionTime, throwExceptionWhenFail = false)
        )
      )
    } else {
      (false, demographicMetadata)
    }
  }

  override def handleCustomerNewSignedInEvent(event: CustomerNewSignedInEvent): Future[Unit] = Profiler(s"$clazz.handleCustomerNewSignedInEvent") {
    val customerId = event.userId
    updateCustomerLock.withLock(customerId) {
      for {
        currUser <- internalGet(customerId)

        demographicMetadata = currUser.demographicMetadata.getOrElse(DemographicMetadata())

        oldFirstLoginTime = demographicMetadata.firstLoginTime
        newFirstLoginTime = oldFirstLoginTime match {
          case Some(_) => oldFirstLoginTime
          case None => event.timestamp.toSome
        }

        oldLastLoginTime = demographicMetadata.lastLoginTime
        newLastLoginTime = oldLastLoginTime match {
          case Some(oldTime) => if (oldTime < event.timestamp) event.timestamp.toSome else oldLastLoginTime
          case None => event.timestamp.toSome
        }

        demographicMetadataForUpdate = demographicMetadata.copy(
          firstLoginTime = newFirstLoginTime,
          lastLoginTime = newLastLoginTime,
          firstLoginDate = CTimeUtils.epochToTimestampTz(newFirstLoginTime, throwExceptionWhenFail = false),
          lastLoginDate = CTimeUtils.epochToTimestampTz(newLastLoginTime, throwExceptionWhenFail = false)
        )

        _ <- internalUpdateUser(
          customerId,
          Customer(userId = customerId.toSome, demographicMetadata = demographicMetadataForUpdate.toSome),
        )

      } yield {}
    }
  }
  // endregion

  // region email normalization
  private val emailNormalizationLockKey = "email_normalization_batch_process"
  private val syncLoginTimeLockKey = "sync_login_time_batch_process"

  /**
   * Normalize emails for customers that have an email but no normalized_email within a specified ID range
   *
   * @param batchSize Number of customers to process in each batch
   * @param fromId    Start of customer ID range (inclusive, -1 means no lower limit)
   * @param toId      End of customer ID range (inclusive, -1 means no upper limit)
   * @return Number of customers processed
   */
  override def normalizeEmailsInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int] = Profiler(s"$clazz.normalizeEmailsInRange") {
    internalBatchProcessLock.tryLock(emailNormalizationLockKey) {
      case true =>
        Profiler(s"$clazz.normalizeEmailsInRange - insideLock") {
          processCustomersWithoutNormalizedEmailInRange(batchSize, fromId, toId, 0)
            .rescue {
              case e: Exception =>
                error(s"Failed to normalize emails in range fromId=$fromId, toId=$toId", e)
                Future.value(-1)
            }
        }
      case _ =>
        info("Email normalization is already running, skipping")
        Future.value(-2)
    }
  }

  /**
   * Process customers in a specific ID range with non-normalized emails
   *
   * @param batchSize      Number of customers to process in each batch
   * @param fromId         Start of customer ID range (inclusive, -1 means no lower limit)
   * @param toId           End of customer ID range (inclusive, -1 means no upper limit)
   * @param totalProcessed Total number of customers processed so far
   * @return Total number of customers processed
   */
  private def processCustomersWithoutNormalizedEmailInRange(batchSize: Int, fromId: Long, toId: Long, totalProcessed: Int): Future[Int] = {
    async {
      customerDAO.fetchCustomersWithoutNormalizedEmailInRange(fromId, toId, batchSize)
    }.flatMap { customers =>
      if (customers.isEmpty) {
        info(s"Completed email normalization in range fromId=$fromId, toId=$toId, processed $totalProcessed customers")
        Future.value(totalProcessed)
      } else {
        val updates = customers.map { customer =>
          normalizeAndUpdateCustomerEmail(customer)
        }

        Future.collect(updates).flatMap { _ =>
          val processed = totalProcessed + customers.size
          info(s"Processed batch of ${customers.size} customers in range, total processed: $processed")

          // If we processed a full batch and there might be more customers, continue processing
          if (customers.size == batchSize && (toId < 0 || customers.last.userId.exists(_.toLong < toId))) {
            val nextFromId = customers.last.userId.map(_.toLong).getOrElse(fromId) + 1
            processCustomersWithoutNormalizedEmailInRange(batchSize, nextFromId, toId, processed)
          } else {
            // Otherwise, we're done
            Future.value(processed)
          }
        }
      }
    }
  }

  /**
   * Normalize and update a customer's email
   *
   * @param customer Customer to update
   * @return Unit
   */
  private def normalizeAndUpdateCustomerEmail(customer: Customer): Future[Unit] = {
    customer.email match {
      case Some(email) if email.nonEmpty =>
        val normalizedEmail = EmailHelper.normalize(email)
        if (customer.normalizedEmail.contains(normalizedEmail)) {
          // Already normalized correctly
          Future.Unit
        } else {
          // Update normalized_email
          async {
            customerDAO.updateNormalizedEmail(customer.userId.get, normalizedEmail)
          }
        }
      case _ =>
        // No email to normalize
        Future.Unit
    }
  }
  // endregion

  // region identity verification data migration
  private val identityVerificationMigrationLockKey = "identity_verification_migration_batch_process"

  override def migrateIdentityVerificationDataInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int] = Profiler(s"$clazz.migrateIdentityVerificationDataInRange") {
    internalBatchProcessLock.tryLock(identityVerificationMigrationLockKey) {
      case true =>
        Profiler(s"$clazz.migrateIdentityVerificationDataInRange - insideLock") {
          processIdentityVerificationMigrationInRange(batchSize, fromId, toId)
            .rescue {
              case e: Exception =>
                error(s"Failed to migrate identity verification data in range fromId=$fromId, toId=$toId", e)
                Future.value(-1)
            }
        }
      case _ =>
        info("migrateIdentityVerificationDataInRange is already running, skipping")
        Future.value(-2)
    }
  }

  private def processIdentityVerificationMigrationInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int] = {
    val finalPromise = Promise[Int]()

    // State for the iteration
    case class BatchState(currentFromId: Long, totalProcessed: Int, batchNumber: Int)

    def processBatch(state: BatchState): Unit = {
      val records = identityVerificationDAO.selectApprovedIdentites(batchSize, state.currentFromId, toId)

      if (records.isEmpty) {
        info(s"Completed identity verification data migration in range fromId=$fromId, toId=$toId, processed ${state.totalProcessed} records")
        finalPromise.setValue(state.totalProcessed)
      } else {
        val recordsToProcess = records.filter(record =>
          record.data.exists(_.documentType.isEmpty) ||
          record.data.exists(_.mrzId.isEmpty) ||
          record.data.exists(_.city.isEmpty)
        )

        val updates = recordsToProcess.map { record =>
          migrateIdentityVerificationRecord(record)
        }

        Future.collect(updates).respond {
          case Return(results) =>
            val successCount = results.count(identity)
            val newTotalProcessed = state.totalProcessed + successCount

            info(s"Processed batch ${state.batchNumber}: ${records.size} records, successful: $successCount, total processed: $newTotalProcessed")

            // Continue with next batch using iteration instead of recursion
            val nextFromId = records.last.id.getOrElse(state.currentFromId) + 1
            val nextState = BatchState(nextFromId, newTotalProcessed, state.batchNumber + 1)

            // Schedule next batch processing asynchronously to avoid stack buildup
            futurePoolLock {
              processBatch(nextState)
            }

          case Throw(exception) =>
            error(s"Error processing batch ${state.batchNumber}", exception)
            finalPromise.setException(exception)
        }
      }
    }

    // Start the iterative processing
    futurePoolLock {
      processBatch(BatchState(fromId, 0, 1))
    }

    finalPromise
  }

  private def migrateIdentityVerificationRecord(record: CustomerIdentityVerification): Future[Boolean] = {
    try {
      val provider = record.provider.getOrElse("SELF_IMPL")

      provider match {
        case "VBD_SDK" =>
          migrateVbdSdkRecord(record)
        case "SELF_IMPL" =>
          migrateSelfImplRecord(record)
        case _ =>
          warn(s"Unknown provider: $provider for record ID: ${record.id}")
          Future.value(false)
      }
    } catch {
      case e: Exception =>
        error(s"Failed to migrate record ID: ${record.id}", e)
        Future.value(false)
    }
  }

  private def migrateVbdSdkRecord(record: CustomerIdentityVerification): Future[Boolean] = {
    record.metadata.flatMap(_.get("document_id").map(_.toString)) match {
      case Some(documentId) =>
        vbdEyePassIntegrationService.fetchDocument(documentId, "vi").flatMap {
          case Some(documentInfo) =>
            val documentType = EKYCDocumentType.get(documentInfo.validationInfo.ocr.get.`type`)
            val mrzId = documentInfo.validationInfo.ocr.flatMap(_.mrzInfo).flatMap(_.code).map(_.value)
            val city = documentInfo.validationInfo.ocr.flatMap(_.city).map(_.value)

            val currentData = record.data.getOrElse(CustomerIdentityDocumentInfo(
              identityType = record.identityType.getOrElse("CCCD"),
              no = record.no.getOrElse("")
            ))

            val updatedData = currentData.copy(
              documentType = documentType.toString.toSome,
              mrzId = mrzId.orElse(currentData.mrzId),
              city = city.orElse(currentData.city)
            )

            if (hasNewData(currentData, updatedData)) {
              async {
                identityVerificationDAO.updateDataFields(record.id.get, updatedData)
                info(s"Successfully updated VBD_SDK record ID: ${record.id} with documentId: $documentId")
                true
              }
            } else {
              info(s"No new data to update for VBD_SDK record ID: ${record.id}")
              Future.value(false)
            }
          case None =>
            warn(s"Failed to fetch document data for VBD_SDK record ID: ${record.id}, documentId: $documentId")
            Future.value(false)
        }
      case None =>
        warn(s"Missing document_id in metadata for VBD_SDK record ID: ${record.id}")
        Future.value(false)
    }
  }

  private def migrateSelfImplRecord(record: CustomerIdentityVerification): Future[Boolean] = {
    (record.identityType, record.identityImages) match {
      case (Some(identityType), Some(images)) if images.nonEmpty =>
        for {
          imageUrls <- Future.collect(images.map(image => customerFileService.getFileUrl(image.fileName)))
          extractedInfo <- aiIntegrationService.extractIdentify(CustomerIdentityDocType.get(identityType), imageUrls)
          _ = info(s"extractedInfo: $extractedInfo")

          documentType = EKYCDocumentType.get(extractedInfo.flatMap(_.documentType).getOrElse(""))
          mrzId = extractedInfo.flatMap(_.mrzId)
          city = extractedInfo.flatMap(_.city)

          currentData = record.data.getOrElse(CustomerIdentityDocumentInfo(
            identityType = identityType,
            no = record.no.getOrElse("")
          ))

          updatedData = currentData.copy(
            documentType = documentType.toString.toSome,
            mrzId = mrzId.orElse(currentData.mrzId),
            city = city.orElse(currentData.city)
          )

          _ = info(s"currentData: $currentData, updatedData: $updatedData")

          result <- if (hasNewData(currentData, updatedData)) {
            async {
              identityVerificationDAO.updateDataFields(record.id.get, updatedData)
              info(s"Successfully updated SELF_IMPL record ID: ${record.id}")
              true
            }
          } else {
            info(s"No new data to update for SELF_IMPL record ID: ${record.id}")
            Future.value(false)
          }
        } yield result
      case _ =>
        warn(s"Missing identityType or identityImages for SELF_IMPL record ID: ${record.id}")
        Future.value(false)
    }
  }

  private def hasNewData(currentData: CustomerIdentityDocumentInfo, updatedData: CustomerIdentityDocumentInfo): Boolean = {
    currentData.documentType.getOrElse("") != updatedData.documentType.getOrElse("") ||
    currentData.mrzId.getOrElse("") != updatedData.mrzId.getOrElse("") ||
    currentData.city.getOrElse("") != updatedData.city.getOrElse("")
  }
  // endregion

  override def syncLoginTimeInRange(batchSize: Int, fromId: Long, toId: Long): Future[Int] = Profiler(s"$clazz.syncLoginTimeInRange") {
    internalBatchProcessLock.tryLock(syncLoginTimeLockKey) {
      case true =>
        Profiler(s"$clazz.syncLoginTimeInRange - insideLock") {
          processSyncLoginTimeCustomers(batchSize, fromId, toId, 0)
            .rescue {
              case e: Exception =>
                error(s"Failed to sync login time in range fromId=$fromId, toId=$toId", e)
                Future.value(-1)
            }
        }
      case _ =>
        info("syncLoginTimeInRange is already running, skipping")
        Future.value(-2)
    }
  }

  private def processSyncLoginTimeCustomers(batchSize: Int, fromId: Long, toId: Long, totalProcessed: Int): Future[Int] = Profiler(s"$clazz.processSyncLoginTimeCustomers") {
    async {
      val filter = CustomerFilter(
        fromId = fromId,
        toId = toId,
        limit = batchSize,
        requiredDevice = true.toSome
      )
      customerDAO.fetchCustomersByFilter(filter)
    }.flatMap { customers =>
      if (customers.isEmpty) {
        info(s"Completed sync login time in range fromId=$fromId, toId=$toId, processed $totalProcessed customers")
        Future.value(totalProcessed)
      } else {
        val updates = customers.map { customer =>
          calculateAndUpdateLoginTimeCustomer(customer)
        }

        Future.collect(updates).flatMap { _ =>
          val processed = totalProcessed + customers.size
          info(s"Processed batch of ${customers.size} customers in range, total processed: $processed")

          // If we processed a full batch and there might be more customers, continue processing
          if (customers.size == batchSize) {
            val nextToId = customers.last.userId.map(_.toLong).getOrElse(toId) - 1
            processSyncLoginTimeCustomers(batchSize, fromId, nextToId, processed)
          } else {
            // Otherwise, we're done
            info(s"Completed sync login time in range fromId=$fromId, toId=$toId, processed $totalProcessed customers")
            Future.value(processed)
          }
        }
      }
    }
  }

  private def calculateAndUpdateLoginTimeCustomer(customer: Customer): Future[Unit] = {
    val deviceAddedTime: Seq[Option[Long]] =
      customer.devices.map(_.map(_.addedTime)).getOrElse(Seq.empty)

    if (deviceAddedTime.isEmpty)
      Future.value(Unit)
    else {
      val hasEmpty = deviceAddedTime.exists(_.isEmpty)
      val flattened = deviceAddedTime.flatten

      val firstLoginTime =
        if (hasEmpty || flattened.isEmpty)
          customer.createdTime
        else
          flattened.min.toSome

      val lastLoginTime = customer.demographicMetadata.flatMap(_.lastLoginTime)
          .orElse(customer.demographicMetadata.flatMap(_.lastAppInteractionTime))
          .orElse(if (flattened.nonEmpty) flattened.max.toSome else None)
          .orElse(customer.createdTime)

      async {
        customerDAO.updateLoginTimeWithoutLocking(
          customer.userId.get,
          firstLoginTime,
          lastLoginTime
        )
      }
    }

  }

}
