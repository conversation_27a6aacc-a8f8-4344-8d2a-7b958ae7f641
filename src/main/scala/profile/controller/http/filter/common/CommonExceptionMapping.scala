package profile.controller.http.filter.common

import com.fasterxml.jackson.core.JsonParseException
import com.google.inject.Singleton
import com.twitter.finagle.http.{Request, Response}
import com.twitter.finatra.http.exceptions.ExceptionMapper
import com.twitter.finatra.http.response.ResponseBuilder
import com.twitter.finatra.jackson.caseclass.exceptions.CaseClassMappingException
import com.twitter.inject.Logging
import org.apache.shiro.authc.LockedAccountException
import profile.domain.response.BaseResponse
import profile.exception._
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.exception.{RException, VhmException}
import vn.vhm.common.util.JsonHelper

import javax.inject.Inject


/**
 * <AUTHOR>
 */
class CommonExceptionMapping @Inject()(response: ResponseBuilder) extends ExceptionMapper[Exception] with Logging {
  override def toResponse(request: Request, ex: Exception): Response = {
    ex match {
      case x: NotExistUserException => response.badRequest(Map("error" -> x.error, "msg" -> x.getMessage))

      case x: InvalidCredentialException => respBadRequest(ex, Map("invalid_credential" -> true) ++ x.data, "invalid_credential")
      case x: UserBlockedException =>
        respBadRequest(
          ex,
          Map("invalid_credential" -> true, "locked" -> true, "msg" -> "Tài khoản của bạn đã bị tạm khóa. Vui lòng liên hệ CSKH VinClub để được giải quyết") ++ x.data,
          "user_locked"
        )

      case _: UnAuthenException => respBadRequest(ex, Map("not_authen" -> true), "not_authen")
      //      case _: UnAuthenException => response.unauthorized(BaseResponse(-1, data = Some(Map("invalid_credential" -> true)), error = Some("invalid_credential")))

      case x: UnAuthorizeException => response.unauthorized(BaseResponse(-1, msg = Some(x.getMessage), error = Some("not_allow")))

      case x: AccessTokenInvalid => respBadRequest(ex, Map("invalid_access_token" -> true, "msg" -> x.getMessage), "invalid_access_token")

      case x: RefreshTokenInvalid => respBadRequest(ex, Map("invalid_refresh_token" -> true, "msg" -> x.getMessage), "invalid_refresh_token")

      case _: NeedPhoneException => response.unauthorized(BaseResponse(-1, data = Some(Map("verify_phone" -> false)), error = Some("need_verify_phone")))

      case _: InvalidTokenPhoneException => respBadRequest(ex, Map("token_phone" -> false, "msg" -> "token not exist"), "need_token_phone")

      case _: InvalidTokenException => respBadRequest(ex, Map("token" -> false, "msg" -> "token not exist"), "need_token")

      case x: InvalidVerifyPhoneCodeException => respBadRequest(ex, Map("verify_phone_code" -> false, "msg" -> "code not exist", "expired_in_seconds" -> x.expiredIn) ++ x.data, "invalid_phone_verify_code")

      case x: InvalidVerifyEmailCodeException => respBadRequest(ex, Map("verify_email_code" -> false, "msg" -> "code not exist", "expired_in_seconds" -> x.expiredIn) ++ x.data, "invalid_email_verify_code")

      case x: InvalidVerifyPnlRequestException => respBadRequest(ex, Map("verify_pnl_request" -> false, "msg" -> x.getMessage) ++ x.data, "invalid_pnl_verify_request")

      case _: ExceedQuotaException => respBadRequest(ex, Map("token_captcha" -> false, "msg" -> "please enter captcha"), "need_token_captcha")
      case x: LockedException => respBadRequest(ex, Map("locked" -> true) ++ x.data, "locked_error")
      case _: InvalidTokenCaptchaException => respBadRequest(ex, Map("token_captcha" -> false, "msg" -> "please retry with other captcha"), "invalid_token_captcha")
      case x: ShieldVerifyFailedException => respShieldVerifyFailed(x)
      case x: ShieldRequiredException => respShieldRequired(x)

      case x: AlreadyExistEmailException => respBadRequest(ex, Map("exist_email" -> true, "msg" -> "already exist email") ++ x.data, "already_exist_email")
      case x: AlreadyExistByNormalizedEmailException => respBadRequest(ex, Map("exist_email" -> true, "existed_by_normalized_email" -> true, "msg" -> "already exist by normalized email or email with alias", "requested_email" -> x.email) ++ x.data, "already_exist_by_normalized_email")
      case x: AlreadyExistPhoneException => respBadRequest(ex, Map("exist_phone" -> true, "msg" -> "already exist phone number") ++ x.data, "already_exist_phone")
      case x: AlreadyExistUserForInternalException => respBadRequest(ex, Map("exist_user" -> true, "msg" -> "already exist user", "vclub_user_id" -> x.username.toLong) ++ x.data, "already_exist_user")
      case _: AlreadyExistUserOfOAuth => respBadRequest(ex, Map("exist_oauth" -> true, "msg" -> "already exist oauth"), "already_exist_oauth")

      case _: NotExistEmailException => respBadRequest(ex, Map("exist_email" -> false, "msg" -> "not exist email"), "not_exist_email")
      case _: NotExistPhoneException | _: NotExistUserByPhone => respBadRequest(ex, Map("exist_phone" -> false, "msg" -> "not exist phone number"), "not_exist_phone")
      case e: NotExistUserOfOAuth => respBadRequest(ex, Map("exist_oauth" -> false, "msg" -> "not exist oauth", "info" -> e.info), "not_exist_oauth")
      case e: UnSupportEmailException => respBadRequest(ex, Map("unsupport_email" -> true, "msg" -> "Unsupport register by this email", "category" -> e.category), "unsupport_register_email")

      case e: NeedRegisterException => respBadRequest(ex, Map("need_register" -> true, "msg" -> "please register", "info" -> e.info), "need_register")
      //      case _: NotAuthTypeUserPassException => respBadRequest(ex, Map("auth_type_user_pass" -> false, "msg" -> "authen is not type of username/password"), "invalid_auth_type_user_pass")
      case _: InvalidPhoneException => respBadRequest(ex, Map("invalid_phone" -> true, "msg" -> "invalid phone number"), "invalid_phone")
      case _: InvalidEmailException => respBadRequest(ex, Map("invalid_email" -> true, "msg" -> "invalid email"), "invalid_email")

      case _: UnsupportedLoginType => respBadRequest(ex, Map("invalid_login_type" -> true, "msg" -> "invalid login type"), "invalid_login_type")

      case e: InternalException => respBadRequestWithLog(ex, Map(e.error -> true, "msg" -> e.errorMsg), e.error)
      case e@(_: SessionExpiredException | _: EmptyEmailException) =>
        logger.error(s"${e.getClass.getSimpleName}: ${ex.getLocalizedMessage}")
        response.badRequest(BaseResponse(-1, None, error = Some("invalid_session"), errorMsg = ex.getLocalizedMessage.toSome))

      case ex: RException =>
        response.badRequest(BaseResponse(-1, None, Map("msg" -> ex.getMessage).toSome, error = ex.error.toSome))

      case ex: VhmException =>
        response.badRequest(BaseResponse(-1, None, Map("msg" -> ex.getMessage).toSome, error = ex.error.toSome))

      case e: java.sql.SQLException =>
        logger.error(s"Failed ${request.contentString}", e)
        response.badRequest(BaseResponse(-1, None, error = "internal_error".toSome, errorMsg = "Contact admin for more information.".toSome))

      case _ =>
        logger.error(ex.getLocalizedMessage, ex)
        response.badRequest(BaseResponse(-1, Some(ex.getLocalizedMessage), error = ex.getClass.getSimpleName.toSome))
    }
  }

  private def respBadRequest(ex: Exception, map: Map[String, Any], error: String) = {
    logger.error(s"${ex.getLocalizedMessage} => Response: ${if (map == null) "<empty>" else JsonHelper.toJson(map)}")
    response.badRequest(BaseResponse(-1, data = Some(map), error = Some(error)))
  }

  private def respBadRequestWithLog(ex: Exception, map: Map[String, Any], error: String) = {
    logger.error(s"${ex.getLocalizedMessage} => Response: ${if (map == null) "<empty>" else JsonHelper.toJson(map)}", ex)
    response.badRequest(BaseResponse(-1, data = Some(map), error = Some(error)))
  }

  private def respShieldRequired(ex: ShieldRequiredException) = {
    logger.error(s"${ex.getLocalizedMessage}")
    response.badRequest(BaseResponse(ex.code, message = Some(Seq(ex.message)), data = Some(Map("shield_token" -> ex.shieldToken, "hold_time_in_second" -> ex.holdTimeInSecond, "reusable" -> ex.reusable))))
  }

  private def respShieldVerifyFailed(ex: ShieldVerifyFailedException) = {
    logger.error(s"${ex.getLocalizedMessage}")
    response.badRequest(BaseResponse(ex.code, message = Some(Seq(ex.message)), data = Some(Map("reason" -> ex.reason))))
  }
}

@Singleton
class CaseClassExceptionMapping @Inject()(response: ResponseBuilder) extends ExceptionMapper[CaseClassMappingException] with Logging {
  override def toResponse(request: Request, throwable: CaseClassMappingException): Response = {
    response.badRequest(
      BaseResponse(
        -1,
        data = Some(Map("invalid_param" -> true, "msg" -> throwable.errors.head.getMessage())),
        error = Some("invalid_param")
      ))
  }
}

@Singleton
class JsonParseExceptionMapping @Inject()(response: ResponseBuilder) extends ExceptionMapper[JsonParseException] with Logging {
  override def toResponse(request: Request, throwable: JsonParseException): Response = {
    response.badRequest(
      BaseResponse(
        -1,
        data = Some(Map("invalid_json_format" -> true)),
        error = Some("invalid_json_format")
      ))
  }
}
