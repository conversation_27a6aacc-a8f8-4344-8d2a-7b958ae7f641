package profile.domain.customer

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.{JsonDeserialize, JsonSerialize}
import profile.domain.{BytesFile, VClubFileInfo}
import profile.service.CustomerIdentityDocumentInfo
import profile.util.VHMConfig
import vn.vhm.common.domain.Implicits.ImplicitAny
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.exception.UnsupportedException
import vn.vhm.common.util.JsonHelper
import vn.vhm.jdbc.postgres.PostgreSqlDAO
import vn.vhm.jdbc.{JdbcR<PERSON>ord, SqlFieldMissing}

import javax.sql.DataSource

/**
 * <AUTHOR> 9/19/24 06:53
 */
object CustomerIdentityVerification {

  val TBL_NAME = "customer_identity_verification"

  def field(f: String): String = f

  val ID = field("id")
  val CUSTOMER_ID = field("customer_id")

  val IDENTITY_TYPE = field("identity_type")
  val NO = field("no")

  val DATA = field("data")

  val IDENTITY_IMAGES = field("identity_images")
  val FACE_IMAGES = field("face_images")


  val CREATED_AT = field("created_at")
  val CREATED_BY = field("created_by")

  val UPDATED_AT = field("updated_at")
  val UPDATED_BY = field("updated_by")

  val REQUESTED_AT = field("requested_at")
  val REQUESTED_BY = field("requested_by")

  val APPROVAL_STATUS = field("approval_status")
  val APPROVED_AT = field("approved_at")
  val APPROVED_BY = field("approved_by")

  val METADATA = field("metadata")

  val SCORES = field("scores")

  val ACTIVE = field("active")

  val CHIP_DATA = field("chip_data")

  val PROVIDER = field("provider")

  val primaryKeys = Seq(ID)

  val fields = Seq(
    ID, CUSTOMER_ID,
    IDENTITY_TYPE, NO, DATA,
    IDENTITY_IMAGES, FACE_IMAGES,
    CREATED_AT, UPDATED_AT,
    APPROVAL_STATUS, APPROVED_AT, APPROVED_BY,
    SCORES,
    METADATA,
    CHIP_DATA,
    ACTIVE,
    PROVIDER
  )

}

import profile.domain.customer.CustomerIdentityVerification._

case class CustomerIdentityVerificationScores(
                                               @JsonDeserialize(contentAs = classOf[java.lang.Double])
                                               faceMatching: Option[Double] = None,

                                               faceMatchingLabel: Option[String] = None,

                                               @JsonDeserialize(contentAs = classOf[java.lang.Double])
                                               faceChipMatching: Option[Double] = None,

                                               faceChipMatchingLabel: Option[String] = None,
                                             )


case class CustomerIdentityChipData(
                                     documentNo: Option[String] = None,
                                     dob: Option[String] = None,
                                     doe: Option[String] = None,
                                     nationality: Option[String] = None,
                                     documentNumber: Option[String] = None,
                                     surname: Option[String] = None,
                                     givenName: Option[String] = None,
                                     gender: Option[String] = None,
                                     activeAuth: Option[Boolean] = None,
                                     passiveAuth: Option[Boolean] = None,
                                     chipImage: Option[Seq[VClubFileInfo]] = None
                                   )

case class CustomerIdentityVerification(
                                         @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                         var id: Option[Long] = None,
                                         @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                         var customerId: Option[Long] = None,

                                         var identityType: Option[String] = None,
                                         var no: Option[String] = None,
                                         var data: Option[CustomerIdentityDocumentInfo] = None,

                                         var identityImages: Option[Seq[VClubFileInfo]] = None,
                                         var faceImages: Option[Seq[VClubFileInfo]] = None,

                                         @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                         var createdAt: Option[Long] = None,
                                         @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                         var updatedAt: Option[Long] = None,

                                         var approvalStatus: Option[String] = None,
                                         @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                         var approvedAt: Option[Long] = None,
                                         var approvedBy: Option[String] = None,

                                         var scores: Option[CustomerIdentityVerificationScores] = None,
                                         var metadata: Option[Map[String, Any]] = None,
                                         var chipData: Option[CustomerIdentityChipData] = None,

                                         var active: Option[Boolean] = None,

                                         var provider: Option[String] = "SELF_IMPL".toSome

                                       ) extends JdbcRecord {

  def getPrimaryKeys(): Seq[String] = primaryKeys

  override def getFields(): Seq[String] = fields

  override def isJsonField(field: String): Boolean = {
    field == DATA || field == IDENTITY_IMAGES || field == FACE_IMAGES ||
      field == METADATA ||
      field == SCORES ||
      field == CHIP_DATA
  }

  override def setValues(field: String, value: Any): Unit = field match {
    case ID => id = value.asOptLong
    case CUSTOMER_ID => customerId = value.asOptLong

    case IDENTITY_TYPE => identityType = value.asOpt
    case NO => no = value.asOpt
    case DATA => data = value.asOptString.map(JsonHelper.fromJson[CustomerIdentityDocumentInfo](_))

    case IDENTITY_IMAGES => identityImages = value.asOptString.map(JsonHelper.fromJson[Seq[VClubFileInfo]](_))
    case FACE_IMAGES => faceImages = value.asOptString.map(JsonHelper.fromJson[Seq[VClubFileInfo]](_))

    case CREATED_AT => createdAt = value.asOptLong
    case UPDATED_AT => updatedAt = value.asOptLong

    case APPROVAL_STATUS => approvalStatus = value.asOpt
    case APPROVED_AT => approvedAt = value.asOptLong
    case APPROVED_BY => approvedBy = value.asOpt

    case SCORES => scores = value.asOptString.map(JsonHelper.fromJson[CustomerIdentityVerificationScores](_))
    case METADATA => metadata = value.asOptString.map(JsonHelper.fromJson[Map[String, Any]](_))
    case CHIP_DATA => chipData = value.asOptString.map(JsonHelper.fromJson[CustomerIdentityChipData](_))

    case ACTIVE => active = value.asOpt
    case PROVIDER => provider = value.asOpt

    case _ => throw SqlFieldMissing(field, value)
  }

  override def getValue(field: String): Option[Any] = field match {

    case ID => id
    case CUSTOMER_ID => customerId

    case IDENTITY_TYPE => identityType
    case NO => no
    case DATA => data.map(JsonHelper.toJson(_))

    case IDENTITY_IMAGES => identityImages.map(JsonHelper.toJson(_))
    case FACE_IMAGES => faceImages.map(JsonHelper.toJson(_))

    case CREATED_AT => createdAt
    case UPDATED_AT => updatedAt

    case APPROVAL_STATUS => approvalStatus
    case APPROVED_AT => approvedAt
    case APPROVED_BY => approvedBy

    case SCORES => scores.map(JsonHelper.toJson(_))
    case METADATA => metadata.map(JsonHelper.toJson(_))
    case CHIP_DATA => chipData.map(JsonHelper.toJson(_))

    case ACTIVE => active
    case PROVIDER => provider

    case _ => throw SqlFieldMissing(field)
  }

}

case class CustomerIdentityVerificationDAO(ds: DataSource) extends PostgreSqlDAO[CustomerIdentityVerification] {

  override def createRecord(): CustomerIdentityVerification = CustomerIdentityVerification()

  override val table: String = CustomerIdentityVerification.TBL_NAME

  override val sqlLogger = VHMConfig.sqlLogger.getOrElse(super.sqlLogger)

  private val fieldsString = fields.mkString(",")

  def existTypeAndNoAndApprovalStatus(identityType: String,
                                      no: String, approvalStatuses: Seq[String],
                                      excludeCustomerId: Option[Long]): Boolean = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$IDENTITY_TYPE = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](identityType)

    conditions.append(s"$NO = ?")
    conditionsValue.append(no)

    conditions.append(s"$ACTIVE = ?")
    conditionsValue.append(true)

    if (approvalStatuses.nonEmpty) {
      conditions.append(s"$APPROVAL_STATUS IN (${approvalStatuses.map(_ => "?").mkString(",")})")
      conditionsValue.appendAll(approvalStatuses)
    }

    excludeCustomerId.foreach(customerId => {
      conditions.append(s"$CUSTOMER_ID != ?")
      conditionsValue.append(customerId)
    })

    val query =
      s"""
         | SELECT 1
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")}) AND $APPROVAL_STATUS != '' AND $APPROVAL_STATUS IS NOT NULL
         |""".stripMargin

    execute(executeQuery(query, conditionsValue)(rs => {
      if (rs.next()) rs.getInt(1) == 1 else false
    })(_))

  }

  def deactivateByCustomerIdsAndActive(customerIds: Seq[Long], active: Boolean): Unit = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$ACTIVE = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](active)

    if (customerIds.isEmpty) throw UnsupportedException("customerIds empty")

    conditions.append(s"$CUSTOMER_ID IN (${customerIds.map(_ => "?").mkString(",")})")
    conditionsValue.appendAll(customerIds)

    val query =
      s"""
         |UPDATE $table
         |SET $ACTIVE = false
         |WHERE (${conditions.mkString(" AND ")})
         |""".stripMargin

    execute(executeUpdate(query, conditionsValue)(_))
  }

  def select(id: Long): Option[CustomerIdentityVerification] = {
    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE $ID = ?
         |""".stripMargin
    execute(executeQuery(query, Seq(id))(rs => {
      if (rs.next()) Some(parseResult(rs)) else None
    })(_))
  }

  /**
   * Query records with approval_status='SUCCESS' and missing documentType, mrzId, or city fields
   * within a specified ID range for batch processing
   */
  def selectApprovedIdentites(batchSize: Int, fromId: Long, toId: Long): Seq[CustomerIdentityVerification] = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$APPROVAL_STATUS = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any]("SUCCESS")

    conditions.append(s"$ACTIVE = ?")
    conditionsValue.append(true)

    if (fromId > -1) {
      conditions.append(s"$ID >= ?")
      conditionsValue.append(fromId)
    }

    if (toId > -1) {
      conditions.append(s"$ID <= ?")
      conditionsValue.append(toId)
    }

    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE ${conditions.mkString(" AND ")}
         | ORDER BY $ID ASC
         | LIMIT ?
         |""".stripMargin

    conditionsValue.append(batchSize)

    execute(executeQuery(query, conditionsValue)(rs => {
      val results = scala.collection.mutable.ListBuffer[CustomerIdentityVerification]()
      while (rs.next()) {
        results.append(parseResult(rs))
      }
      results.toSeq
    })(_))
  }

  /**
   * Update the data field for a specific record with new documentType, mrzId, and city values
   */
  def updateDataFields(id: Long, updatedData: CustomerIdentityDocumentInfo): Unit = {
    val query =
      s"""
         |UPDATE $table
         |SET $DATA = ?::jsonb, $UPDATED_AT = ?
         |WHERE $ID = ?
         |""".stripMargin

    val updatedAt = System.currentTimeMillis()
    execute(executeUpdate(query, Seq(JsonHelper.toJson(updatedData), updatedAt, id))(_))
  }

}
