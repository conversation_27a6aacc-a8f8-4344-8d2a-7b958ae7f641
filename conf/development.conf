server {
  http {
    port = ":8080"
    max_header_size_in_kb = 16
  }
  thrift {
    port = ":8084"
  }
  admin {
    disable = true
  }
}

debug_schedule {
  log_profiler = false
}

main {
  sql_debug = true

  list_pnl = [
    "TEST_PNL",
    "VINFAST",
    "VIN<PERSON><PERSON><PERSON>",
    "VINMEC",
    "VINPEARL",
    "VINSCHOOL",
    "GSM",
    "FGF",
    "VGREEN"
  ]

  list_pnl_mapping_by_identity_doc = [
    "VINFAST",
    "VINHOMES"
  ]

  list_customer_score_type = [
    "HD_VIKKI",
    "VCLUB"
  ]

  internal_secret_key = "3rp3dwH0dmUGG0zHjDarvTIS9KJvcLlhuZ3JGDDhK4U"
  internal_identify_verification_secret_key = "3rp3dwH0dmUGG0zHjDarvTIS9KJvcLlhuZ3JGDDhK4U"
}

common {
  check_recent = false
  quota_ip = 100
  lock_in_second = 300

  redis_lock_ttl_in_minute = 10
}

mysql {
  driver = "com.mysql.cj.jdbc.Driver"
  host = "localhost"
  port = 2216
  user = "vclub_user"
  pass = "vclub_pass"
  db = "vclub-customer-profile"

  init_sql = ["conf/database/init_mysql_structure.sql", "conf/database/init_mysql_data.sql"]

}

postgresql {
  driver = "org.postgresql.Driver"
  host = "localhost"
  port = 2432
  user = "vclub_user"
  pass = "vclub_pass"
  db = "vinclub_db"
  schema = "core_db"

  init_sql = []

}

caas {

  salt = "$2a$10$DKoIBxtDhEX5.zHnsNCz1O"
  old_salt = ""

  verify_phone {
    facebook = true
    u_p = true
    google = true
    apple = true
  }

  oauth {
    google {
      app_id = []
    }
    facebook {
      app_id_old = ""
      app_secret_old = ""

      app_id = ""
      app_secret = ""
    }
    apple {
      auth_url = "https://appleid.apple.com/auth/token"
      client_id = "vn.vinclub"
      team_id = ""
      key_id = ""
      private_key_path = "conf/apple_token/Vinhomes_AuthKey_....p8"
    }
  }
}

ssdb {
  common {
    host = "localhost"
    port = 8888
    timeoutInMs = 60000
  }
  data {
    host = "localhost"
    port = 8888
    timeoutInMs = 60000

    hash_customer_ranking = "vclub:data:customer_ranking"
    zset_customer_ranking = "vclub:data:customer_ranking:last_change"

    hash_cdp_profile = "vclub:data:cdp_profile"
    zset_cdp_profile = "vclub:data:cdp_profile:last_change"
  }
}

verify_phonenumber_service {
  message_template = "Ma xac thuc VinClub cua ban la $code"
  message_template_for_android = "Ma xac thuc VinClub cua ban la $code"
  message_template_for_ios = "Ma xac thuc VinClub cua ban la $code"
  code_expire_time_in_second = 180

  constant_otp {
    "84934634636" = "032517"
  }

  message_template_by_prefix = [
    // vinaphone
    {
      prefix_numbers = [
        "088", "091", "094", "081", "082", "083", "084", "085",
        "8488", "8491", "8494", "8481", "8482", "8483", "8484", "8485",
        "+8488", "+8491", "+8494", "+8481", "+8482", "+8483", "+8484", "+8485"
      ]
      message_template = "Ma xac thuc VinClub cua ban la $code"
      message_template_for_android = "Ma xac thuc VinClub cua ban la $code.\n\nfF7C1ykHj3j"
      message_template_for_ios = "Ma xac thuc VinClub cua ban la $code.\n\<EMAIL> #$code"
    },
    // viettel
    {
      prefix_numbers = [
        "086", "096", "097", "098", "039", "038", "037", "036", "035", "034", "033", "032",
        "8486", "8496", "8497", "8498", "8439", "8438", "8437", "8436", "8435", "8434", "8433", "8432",
        "+8486", "+8496", "+8497", "+8498", "+8439", "+8438", "+8437", "+8436", "+8435", "+8434", "+8433", "+8432"
      ]
      message_template = "Ma xac thuc VinClub cua ban la $code"
      message_template_for_android = "Ma xac thuc VinClub cua ban la $otp.\n\nfF7C1ykHj3j"
      message_template_for_ios = "Ma xac thuc VinClub cua ban la $otp.\n\<EMAIL> #$otp"
    }
  ]

}

sms {
  src_delivery = "auth_system"
  receivers_status_key = "receivers_status"
  quota_phonenumber = 100
  quota_phonenumber_expire_time_in_second = 7200
  enable = false
}

email {
  enable = false

  // EmailHelper configuration
  helper {
    check_disposable_email = true
    check_domain_blacklist = true
    check_domain_whitelist = false
    check_email_blacklist = true
    check_email_whitelist = false
  }
}

delivery_service {
  thrift {
    host = "eks-svc-stag.vinhomes.vn"
    port = 31145
  }
}

kafka {

  pnl_customer_historical_consumer {
    topic_regex = "^historical\\.pnl_customer_(.+)"
    bootstrap.servers = "localhost:9092"
    group.id = "vclub-customer-svc-pnl-customer-historical-group-dev"
    client.id = "vclub-customer-svc-pnl-customer-historical-client-dev"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
    }
  }

  customer_new_signedin_event_consumer {
    topics = ["vclub_customer.new_signedin"]
    bootstrap.servers = "localhost:9092"
    group.id = "vclub-customer-svc-customer-new-signedin-group-dev"
    client.id = "vclub-customer-svc-customer-new-signedin-client-dev"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
    }
  }
  customer_identity_verification_event_consumer_enable = true
  customer_identity_verification_event_consumer {
    topics = ["system_event_customer_identity_verification"]
    bootstrap.servers = "localhost:9092"
    group.id = "vclub-customer-svc-customer-identity-verification-group-dev"
    client.id = "vclub-customer-svc-customer-identity-verification-client-dev"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
    }
  }

  producer {
    bootstrap.servers = "localhost:9092"
    acks = "all"
    linger.ms = 1
  }

  customer_tier_changed_topic = "vclub_customer.tier_changed"
  customer_historical_topic = "historical.vclub_customer"
  pnl_customer_change_trigger_log_topic = "log.vclub_customer.pnl_customer_change_trigger"
  customer_new_signedin_topic = "vclub_customer.new_signedin"
  customer_auto_approve_identity_verification_topic = "vclub_customer.auto_approve_identity_verification"
}

kafka_delivery {
  producer {
    bootstrap.servers = "localhost:9092"
    acks = "all"
    linger.ms = 1
  }
  topic = "personal_message_delivery"
}

session {
  jwt {
    algo = "RS256"
    key_id = "5572d7e3-5fc46"
    secret_key_rs256_path = "conf/rs256-key/prod/jwtRS256.key"
    public_key_rs256_path = "conf/rs256-key/prod/jwtRS256.key.pub"

    access_token_expire_in_second = 1800
    refresh_token_expire_in_second = 86400000
  }
}

recaptcha_service {
  url_verify = ""
  secret_key = ""
}

authen_service {
  max_login_failed = 2
  // 10p
  lock_login_in_millis = 600000
  reset_num_login_failed_after_in_millis = 600000
}

sync_schedule {

  sync_customer_ranking {
    enable = false
    interval_in_second = 60
  }

  sync_customer_cdp_profile {
    enable = true
    interval_in_second = 60
  }

  deactivate_identity_verification {
    enable = true
    schedule_at_hour = 5
    sync_before_in_hour = 25
  }
}

redis {
  host = "localhost"
  port = 6379
}

vclub_core_service {
  base_url = "http://stag-svc.vinclub.internal:30082/r"
  authz_token = "Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G-LkoaWweSHVyUL3FQmqNQ2m3Lkzpz2nKdtMMmK2s-Ax5Tom3bpktq02KTcJfuEIZc4chqL1Ry7XvBAluUuhCwUa869LY4DXIKJqoWKqH7BA-OCHR9JorjezJ4luG91_EWxmaMWbwfR37UxkMeHvKYfT76FfkErT8SpY2zX84YEtw0sAu2cVIPrA3sF4-0h7_5-kkx9BHUge8__8xKVzvoeQbeHwLKEkC2gBcnCz8N8KlgtEqGPdSACB3T0QjHfal7l68mfpk0ggSjU36TlODPTwN0YPMovOp2Kp443Qw-HBzm3ykOr2WT6Sniyi937m5LT4JEJ0x9paS9mC_9FUAg"
  connect_timeout_ms = 30000
  request_timeout_ms = 30000
}

vclub_shield_service {
  base_url = "http://stag-svc.vinclub.internal:30120/sh"
  connect_timeout_ms = 30000
  request_timeout_ms = 30000
  enable = true
  no_app_check_latest_build_number = 0
}

files_client {
  url = "http://stag-svc.vinclub.internal:31160"
  integrationkey = "69fnbXBHh0lAQ0Xpl3mgoO4MBDcgkM6CO8HFIJt3SkQ"
}

ai_integration {
  extract_identify_url = "http://stag-svc.vinclub.internal:31702/extract_identify"
  matching_faces_url = "http://stag-svc.vinclub.internal:31718/matching_faces"
  extract_address_url = "http://stag-svc.vinclub.internal:31704/extract_detailed_addresses"
}

s3 {
  region = "ap-southeast-1"
  bucket = "vhm-vinclub-stag-file"
  base_path = "customer-dev"

  temporary_bucket = "vhm-vinclub-stag-temporary"
  temporary_prefix_path = "customer-dev"

  customer_bucket = "vhm-vinclub-stag-customer"
  customer_base_path = "customer-dev"
}

imgproxy_service {
  base_url = "http://stag-svc.vinclub.internal:31568"
  service_key = ""
  service_key = ${?IMGPROXY_SERVICE_KEY}
  service_salt = ""
  service_salt = ${?IMGPROXY_SERVICE_SALT}
}

internal_secret_key {
  internal_auth = [
    "dmNsdWItcGFydG5lcjpOeWticGRId0pZZmFpd3NCUXBpcnlqWUV6c05RblB0ejJr",
    "dmNsdWItc3NvOmh3VFgzajdEMmc0U1FiOXp6b0ZtN1NFZDdqRGU1WGRKM3F0WFZNekdB"
  ]
}

vin_bigdata_integration {
  base_url = "https://eyepass-api.vizone.ai/demo"
  app_id = "3708905b-52de-4a4f-98b1-3181bbe86b5a"
  app_secret = "W3GP_o0Bd3IBuRMsy_Fo3-hauAf4NxtrwBlPndrdKwk="
  encrypt_key = "Qf0AiZ0Xl5LZH0SS6pL5aofaNHtBGeIW"

  // document type list: id_card (CCCD 2024), chip_based_id_card (CCCD Chip), white_id_card (CCCD), passport, legacy_id (CMND)
  document_type_supported = "id_card, chip_based_id_card, white_id_card, passport"

  // document verify step list: scan_front_document, scan_back_document, scan_nfc, scan_face
  id_card_verify_steps = "scan_front_document, scan_back_document, scan_nfc, scan_face"
  chip_based_id_card_verify_steps = "scan_front_document, scan_back_document, scan_nfc, scan_face"
  white_id_card_verify_steps = "scan_front_document, scan_back_document, scan_face"
  legacy_id_verify_steps = "scan_front_document, scan_back_document, scan_face"
  passport_verify_steps = "scan_front_document, scan_face"

  // scan face flow configs
  use_face_compare = true
  use_face_liveness = true

  // test scenarios configs
  enable_test_scenarios = ""
}

admin_create_user {
  referral_code = {
    vinfast = "VFACC-ALL"
    vinhomes = "VHMACC-ALL"
    vinmec = "VMACC-ALL"
    vinpearl = "VPACC-ALL"
    vinschool = "VSCACC-ALL"
    gsm = "GSMACC-ALL"
    fgf = "GFACC-ALL"
  }
}

user_registration_queue {
  enable = true
  queue_key = "vclub:user_registration_queue"
  batch_size = 8
  interval_in_second = 10
}