server {
  http {
    port = ":8080"
  }
  thrift {
    port = ":8084"
  }
  admin {
    disable = true
  }
}

debug_schedule {
  log_profiler = false
}

main {
  sql_debug = true

  list_pnl = [
    "TEST_PNL",
    "VINFAST",
    "VINH<PERSON><PERSON>",
    "VINME<PERSON>",
    "VINPEARL",
    "VINSCHOOL",
    "GSM",
    "FGF",
    "VGREEN"
  ]

  list_pnl_mapping_by_identity_doc = [
    "VINFAST",
    "VINHOMES"
  ]

  list_customer_score_type = [
    "HD_VIKKI",
    "VCLUB"
  ]

  internal_secret_key = "3rp3dwH0dmUGG0zHjDarvTIS9KJvcLlhuZ3JGDDhK4U"
  internal_identify_verification_secret_key = "3rp3dwH0dmUGG0zHjDarvTIS9KJvcLlhuZ3JGDDhK4U"
}

common {
  check_recent = false
  quota_ip = 100
  lock_in_second = 300

  redis_lock_ttl_in_minute = 1
}

caas {

  salt = "$2a$10$vQHXdcSwaue/i7PSGlyLhu"
  old_salt = ""

  mysql {
    driver = "com.mysql.cj.jdbc.Driver"
    host = "docker"
    port = 2216
    user = "vclub_user"
    pass = "vclub_pass"
    db = "vclub-customer-profile"

    init_sql = ["conf/database/init_mysql.sql"]
  }

  verify_phone {
    facebook = true
    u_p = true
    google = true
    apple = true
  }

  oauth {
    google {
      app_id = []
    }
    facebook {
      app_id_old = ""
      app_secret_old = ""

      app_id = ""
      app_secret = ""
    }
    apple {
      auth_url = "https://appleid.apple.com/auth/token"
      client_id = "vn.vinclub"
      team_id = ""
      key_id = ""
      private_key_path = "conf/apple_token/Vinhomes_AuthKey_....p8"
    }
  }
}

ssdb {
  common {
    host = "docker"
    port = 8888

    timeoutInMs = 60000
  }
  data {
    host = "docker"
    port = 8888
    timeoutInMs = 60000

    hash_customer_ranking = "vclub:data:customer_ranking"
    zset_customer_ranking = "vclub:data:customer_ranking:last_change"

    hash_cdp_profile = "vclub:data:cdp_profile"
    zset_cdp_profile = "vclub:data:cdp_profile:last_change"
  }
}

verify_phonenumber_service {
  message_template = "VinClub verify code: $code"
  message_template_ = "Xin chao, day la tin nhan duoc gui ra tu he thong VinClub, thong tin ma xac thuc cua ban la $code"
  code_expire_time_in_second = 180

  message_template_by_prefix = [
    // vinaphone
    {
      prefix_numbers = [
        "088", "091", "094", "081", "082", "083", "084", "085",
        "8488", "8491", "8494", "8481", "8482", "8483", "8484", "8485",
        "+8488", "+8491", "+8494", "+8481", "+8482", "+8483", "+8484", "+8485"
      ]
      message_template = "Ma xac thuc VinClub cua ban la $code tai web https://vhm.vn/"
    }
  ]

}

session {
  domain = ".vinclub.vn"
  name = "vclub_ssid"
  timeout_in_ms = ***********
}

sms {
  src_delivery = "auth_system"
  receivers_status_key = "receivers_status"
  quota_phonenumber = 1000
  quota_phonenumber_expire_time_in_second = 5
  enable = false

  accountkit {
    app_id = ""
    app_secret = ""
    redirect_url = "http://localhost:8080/sms"
    region_default = "VN"
  }
}

delivery_service {
  thrift {
    host = "eks-svc-stag.vinhomes.vn"
    port = 31145
  }
}

kafka {

  pnl_customer_historical_consumer {
    topic_regex = "^historical\\.pnl_customer_(.+)"
    bootstrap.servers = "docker:9092"
    group.id = "vclub-customer-svc-pnl-customer-historical-group-dev"
    client.id = "vclub-customer-svc-pnl-customer-historical-client-dev"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
    }
  }

  customer_new_signedin_event_consumer {
    topic_regex = ["vclub_customer.new_signedin"]
    bootstrap.servers = "docker:9092"
    group.id = "vclub-customer-svc-customer-new_signedin-group-dev"
    client.id = "vclub-customer-svc-customer-new_signedin-client-dev"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
    }
  }

  customer_identity_verification_event_consumer_enable = true
  customer_identity_verification_event_consumer {
    topics = ["system_event_customer_identity_verification"]
    bootstrap.servers = "localhost:9092"
    group.id = "vclub-customer-svc-customer-identity-verification-group-dev"
    client.id = "vclub-customer-svc-customer-identity-verification-client-dev"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
    }
  }

  producer {
    bootstrap.servers = "docker:9092"
    acks = "all"
    linger.ms = 1
  }

  customer_tier_changed_topic = "vclub_customer.tier_changed"
  customer_historical_topic = "historical.vclub_customer"
  pnl_customer_change_trigger_log_topic = "log.vclub_customer.pnl_customer_change_trigger"
  pnl_customer_change_trigger_log_topic = "log.vclub_customer.pnl_customer_change_trigger"

  customer_new_signedin_topic = "vclub_customer.new_signedin"
  customer_auto_approve_identity_verification_topic = "vclub_customer.auto_approve_identity_verification"
}

kafka_delivery {
  producer {
    bootstrap.servers = "docker:9092"
    acks = "all"
    linger.ms = 1
  }
  topic = "personal_message_delivery"
}

session {
  jwt {
    algo = "RS256"
    key_id = ""
    secret_key_rs256_path = "conf/rs256-key/stag/jwtRS256.key"
    public_key_rs256_path = "conf/rs256-key/stag/jwtRS256.key.pub"
    access_token_expire_in_second = 1800
    refresh_token_expire_in_second = 86400000
  }
}

recaptcha_service {
  url_verify = ""
  secret_key = ""
}

authen_service {
  max_login_failed = 2
  // 10p
  lock_login_in_millis = 600000
  reset_num_login_failed_after_in_millis = 600000
}

sync_schedule {

  sync_customer_ranking {
    enable = false
    interval_in_second = 60
  }

  sync_customer_cdp_profile {
    enable = true
    interval_in_second = 60
  }

  deactivate_identity_verification {
    enable = true
    schedule_at_hour = 5
    sync_before_in_hour = 25
  }
}

redis {
  host = "docker"
  port = 6379
}

files_client {
  url = "http://stag-svc.vinclub.internal:31160"
  integrationkey = "69fnbXBHh0lAQ0Xpl3mgoO4MBDcgkM6CO8HFIJt3SkQ"
}

ai_integration {
  extract_identify_url = "http://stag-svc.vinclub.internal:31702/extract_identify"
  matching_faces_url = "http://stag-svc.vinclub.internal:31718/matching_faces"
  extract_address_url = "http://stag-svc.vinclub.internal:31704/extract_detailed_addresses"
}

s3 {
  region = "ap-southeast-1"
  bucket = "vhm-vinclub-stag-file"
  base_path = "customer-ci"

  temporary_bucket = "vhm-vinclub-stag-temporary"
  temporary_prefix_path = "customer-ci"

  customer_bucket = "vhm-vinclub-stag-customer"
  customer_base_path = "customer-ci"
}

imgproxy_service {
  base_url = "http://stag-svc.vinclub.internal:31568"
  service_key = ""
  service_key = ${?IMGPROXY_SERVICE_KEY}
  service_salt = ""
  service_salt = ${?IMGPROXY_SERVICE_SALT}
}

internal_secret_key {
  internal_auth = [
    "dmNsdWItcGFydG5lcjpOeWticGRId0pZZmFpd3NCUXBpcnlqWUV6c05RblB0ejJr",
    "dmNsdWItc3NvOmh3VFgzajdEMmc0U1FiOXp6b0ZtN1NFZDdqRGU1WGRKM3F0WFZNekdB"
  ]
}

vin_bigdata_integration {
  base_url = "https://eyepass-api.vizone.ai/demo"
  app_id = "3708905b-52de-4a4f-98b1-3181bbe86b5a"
  app_secret = "W3GP_o0Bd3IBuRMsy_Fo3-hauAf4NxtrwBlPndrdKwk="
  encrypt_key = "Qf0AiZ0Xl5LZH0SS6pL5aofaNHtBGeIW"

  // document type list: id_card (CCCD 2024), chip_based_id_card (CCCD Chip), white_id_card (CCCD), passport, legacy_id (CMND)
  document_type_supported = "id_card, chip_based_id_card, white_id_card, passport"

  // document verify step list: scan_front_document, scan_back_document, scan_nfc, scan_face
  id_card_verify_steps = "scan_front_document, scan_back_document, scan_nfc, scan_face"
  chip_based_id_card_verify_steps = "scan_front_document, scan_back_document, scan_nfc, scan_face"
  white_id_card_verify_steps = "scan_front_document, scan_back_document, scan_face"
  legacy_id_verify_steps = "scan_front_document, scan_back_document, scan_face"
  passport_verify_steps = "scan_front_document, scan_face"

  // scan face flow configs
  use_face_compare = true
  use_face_liveness = true

  // test scenarios configs
  enable_test_scenarios = ""
}

admin_create_user {
  referral_code = {
    vinfast = "VFACC-ALL"
    vinhomes = "VHMACC-ALL"
    vinmec = "VMACC-ALL"
    vinpearl = "VPACC-ALL"
    vinschool = "VSCACC-ALL"
    gsm = "GSMACC-ALL"
    fgf = "GFACC-ALL"
  }
}

user_registration_queue {
  enable = true
  queue_key = "vclub:user_registration_queue"
  batch_size = 8
  interval_in_second = 10
}
